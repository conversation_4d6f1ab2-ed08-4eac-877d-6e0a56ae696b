import os
import shutil
from pathlib import Path

def fix_scannet_structure():
    # 基础路径
    base_path = Path('data/scannetv2/scans')
    
    # 获取所有场景文件
    scene_files = list(base_path.glob('scene*_*_*.ply'))
    
    for scene_file in scene_files:
        # 从文件名中提取场景ID（例如：从scene0707_00_vh_clean_2.ply提取scene0707_00）
        scene_id = '_'.join(scene_file.stem.split('_')[:2])
        
        # 创建场景目录
        scene_dir = base_path / scene_id
        scene_dir.mkdir(exist_ok=True)
        
        # 移动所有相关文件到场景目录
        for file in base_path.glob(f'{scene_id}_*'):
            if file.is_file():
                shutil.move(str(file), str(scene_dir / file.name))
        
        # 创建空的txt文件（如果需要）
        txt_file = scene_dir / f'{scene_id}.txt'
        if not txt_file.exists():
            with open(txt_file, 'w') as f:
                f.write('axisAlignment = 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1\n')

if __name__ == '__main__':
    fix_scannet_structure() 