import numpy as np
import trimesh
import matplotlib.pyplot as plt
from typing import Optional, List, Tuple


def save_point_cloud(points: np.ndarray, 
                     colors: Optional[np.ndarray] = None,
                     output_path: str = 'point_cloud.ply') -> None:
    """
    保存点云为PLY文件
    
    Args:
        points: 点云坐标 (N, 3)
        colors: 点云颜色 (N, 3), 可选
        output_path: 输出文件路径
    """
    if colors is not None:
        # 确保颜色值在[0,255]范围内
        if colors.max() <= 1.0:
            colors = (colors * 255).astype(np.uint8)
        else:
            colors = colors.astype(np.uint8)
        
        # 创建带颜色的点云
        cloud = trimesh.PointCloud(vertices=points, colors=colors)
    else:
        cloud = trimesh.PointCloud(vertices=points)
    
    cloud.export(output_path)


def save_mesh(vertices: np.ndarray, 
              faces: np.ndarray,
              colors: Optional[np.ndarray] = None,
              output_path: str = 'mesh.ply') -> None:
    """
    保存网格为PLY文件
    
    Args:
        vertices: 顶点坐标 (V, 3)
        faces: 面索引 (F, 3)
        colors: 顶点颜色 (V, 3), 可选
        output_path: 输出文件路径
    """
    mesh = trimesh.Trimesh(vertices=vertices, faces=faces)
    
    if colors is not None:
        if colors.max() <= 1.0:
            colors = (colors * 255).astype(np.uint8)
        mesh.visual.vertex_colors = colors
    
    mesh.export(output_path)


def visualize_instances(points: np.ndarray,
                       instance_labels: np.ndarray,
                       output_path: str = 'instances.ply') -> None:
    """
    可视化实例分割结果
    
    Args:
        points: 点云坐标 (N, 3)
        instance_labels: 实例标签 (N,)
        output_path: 输出文件路径
    """
    # 为每个实例分配不同颜色
    unique_labels = np.unique(instance_labels)
    colors = np.zeros((len(points), 3))
    
    # 生成颜色映射
    cmap = plt.cm.get_cmap('tab20')
    
    for i, label in enumerate(unique_labels):
        if label == -1:  # 背景点设为黑色
            continue
        mask = instance_labels == label
        color = cmap(i % 20)[:3]  # 取RGB，忽略alpha
        colors[mask] = color
    
    save_point_cloud(points, colors, output_path)


def create_bbox_mesh(center: np.ndarray, 
                    size: np.ndarray,
                    rotation: Optional[np.ndarray] = None) -> trimesh.Trimesh:
    """
    创建包围盒网格
    
    Args:
        center: 包围盒中心 (3,)
        size: 包围盒尺寸 (3,)
        rotation: 旋转矩阵 (3, 3), 可选
    
    Returns:
        trimesh.Trimesh: 包围盒网格
    """
    # 创建单位立方体
    box = trimesh.creation.box(extents=size)
    
    # 应用旋转
    if rotation is not None:
        box.apply_transform(trimesh.transformations.rotation_matrix(
            angle=0, direction=[1, 0, 0], point=[0, 0, 0]
        ))
    
    # 平移到中心
    box.apply_translation(center)
    
    return box 