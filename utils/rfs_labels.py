"""
RFS标签定义 - 和DIMR保持一致
25个类别的标签系统
"""

import numpy as np

# RFS标签系统 - 和DIMR完全一致
RFS_CLASSES = (
    'wall', 'floor', 'cabinet', 'bed', 'chair', 'sofa', 'table', 
    'door', 'window', 'bookshelf', 'picture', 'counter', 'desk', 
    'curtain', 'refridgerator', 'shower curtain', 'toilet', 'sink', 
    'bathtub', 'otherfurniture', 'kitchen_cabinet', 'display', 
    'trash_bin', 'other_shelf', 'other_table'
)

# CAD重建类别 - 和DIMR一致
CAD_CLASSES = (
    'table', 'chair', 'bookshelf', 'sofa', 'trash_bin', 
    'cabinet', 'display', 'bathtub'
)

# 类别数量
NUM_RFS_CLASSES = len(RFS_CLASSES)  # 25
NUM_CAD_CLASSES = len(CAD_CLASSES)  # 8

# RFS到CAD的映射 - 和DIMR保持一致
RFS2CAD = {
    2: 5,   # cabinet -> cabinet
    4: 1,   # chair -> chair  
    5: 3,   # sofa -> sofa
    6: 0,   # table -> table
    9: 2,   # bookshelf -> bookshelf
    16: 1,  # toilet -> chair (映射)
    17: 7,  # sink -> bathtub (映射)
    18: 7,  # bathtub -> bathtub
    21: 6,  # display -> display
    22: 4,  # trash_bin -> trash_bin
}

RFS2CAD_arr = np.ones(30) * -1
for k, v in RFS2CAD.items():
    RFS2CAD_arr[k] = v 