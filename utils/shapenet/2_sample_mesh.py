import sys
sys.path.append('.')
import argparse
import os
from glob import glob
import trimesh
import numpy as np
from external.libmesh import check_mesh_contains
from external import binvox_rw, voxels
from multiprocessing import Pool
from functools import partial

def parse_args():
    '''Parameters'''
    parser = argparse.ArgumentParser('Prepare ShapeNetv2 Data.')
    parser.add_argument('--in_folder', type=str, default='datasets/ShapeNetv2_data/watertight',
                        help='Path to input watertight meshes.')
    parser.add_argument('--n_proc', type=int, default=12,
                        help='Number of processes to use.')
    parser.add_argument('--resize', action='store_true',
                        help='When active, resizes the mesh to bounding box.')
    parser.add_argument('--bbox_in_folder', type=str, default='datasets/ShapeNetCore.v2',
                        help='Path to other input folder to extract'
                             'bounding boxes.')
    parser.add_argument('--pointcloud_folder', type=str, default='datasets/ShapeNetv2_data/pointcloud',
                        help='Output path for point cloud.')
    parser.add_argument('--pointcloud_size', type=int, default=100000,
                        help='Size of point cloud.')
    parser.add_argument('--voxels_folder', type=str, default='datasets/ShapeNetv2_data/voxel',
                        help='Output path for voxelization.')
    parser.add_argument('--voxels_res', type=int, default=[16],
                        help='Resolution for voxelization.')
    parser.add_argument('--points_folder', type=str, default='datasets/ShapeNetv2_data/point',
                        help='Output path for points.')
    parser.add_argument('--points_size', type=int, default=100000,
                        help='Size of points.')
    parser.add_argument('--points_uniform_ratio', type=float, default=0.5,
                        help='Ratio of points to sample uniformly'
                             'in bounding box.')
    parser.add_argument('--points_sigma', type=float, default=0.01,
                        help='Standard deviation of gaussian noise added to points'
                             'samples on the surfaces.')
    parser.add_argument('--points_padding', type=float, default=0.1,
                        help='Additional padding applied to the uniformly'
                             'sampled points on both sides (in total).')
    parser.add_argument('--mesh_folder', type=str, default='datasets/ShapeNetv2_data/watertight_scaled',
                        help='Output path for mesh.')
    parser.add_argument('--overwrite', action='store_true',
                        help='Whether to overwrite output.')
    parser.add_argument('--float16', action='store_true',
                        help='Whether to use half precision.')
    parser.add_argument('--packbits', action='store_true',
                        help='Whether to save truth values as bit array.')
    return parser.parse_args()

def export_pointcloud(mesh, filename, loc, scale, args):
    if not args.overwrite and os.path.exists(filename):
        print('Pointcloud already exist: %s' % filename)
        return
    points, face_idx = mesh.sample(args.pointcloud_size, return_index=True)
    normals = mesh.face_normals[face_idx]

    # Compress
    if args.float16:
        dtype = np.float16
    else:
        dtype = np.float32

    points = points.astype(dtype)
    normals = normals.astype(dtype)

    print('Writing pointcloud: %s' % filename)
    np.savez(filename, points=points, normals=normals, loc=loc, scale=scale)

def export_voxels(mesh, dirname, clsname, modelname, loc, scale, args):
    if not mesh.is_watertight:
        print('Warning: mesh %s/%s is not watertight!'
              'Cannot create voxelization.' % (clsname, modelname))
        return

    for res in args.voxels_res:
        filename = os.path.join(dirname, str(res), clsname, modelname + '.binvox')
        if not args.overwrite and os.path.exists(filename):
            print('Voxels already exist: %s' % filename)
            return
        if not os.path.exists(os.path.dirname(filename)):
            os.makedirs(os.path.dirname(filename))
        voxels_occ = voxels.voxelize_ray(mesh, res)
        voxels_out = binvox_rw.Voxels(voxels_occ, (res,) * 3,
                                      translate=loc, scale=scale,
                                      axis_order='xyz')
        print('Writing voxels: %s' % filename)
        with open(filename, 'bw') as f:
            voxels_out.write(f)

def export_points(mesh, filename, loc, scale, args):
    if not mesh.is_watertight:
        print('Warning: mesh %s is not watertight!'
              'Cannot sample points.' % filename)
        return
    if not args.overwrite and os.path.exists(filename):
        print('Points already exist: %s' % filename)
        return

    n_points_uniform = int(args.points_size * args.points_uniform_ratio)
    n_points_surface = args.points_size - n_points_uniform

    boxsize = 1 + args.points_padding
    points_uniform = np.random.rand(n_points_uniform, 3)
    points_uniform = boxsize * (points_uniform - 0.5)
    points_surface = mesh.sample(n_points_surface)
    points_surface += args.points_sigma * np.random.randn(n_points_surface, 3)
    points = np.concatenate([points_uniform, points_surface], axis=0)

    occupancies = check_mesh_contains(mesh, points)

    # Compress
    if args.float16:
        dtype = np.float16
    else:
        dtype = np.float32

    points = points.astype(dtype)

    if args.packbits:
        occupancies = np.packbits(occupancies)

    print('Writing points: %s' % filename)
    np.savez(filename, points=points, occupancies=occupancies,
             loc=loc, scale=scale)

def export_mesh(mesh, filename, loc, scale, args):
    if not args.overwrite and os.path.exists(filename):
        print('Mesh already exist: %s' % filename)
        return
    print('Writing mesh: %s' % filename)
    mesh.export(filename)

def process_path(in_path, args):
    mesh = trimesh.load(in_path, process=False)
    clsname = in_path.split('/')[3]
    modelname = os.path.splitext(os.path.basename(in_path))[0]

    if not args.resize:
        loc = np.zeros(3)
        scale = 1.
    else:
        if args.bbox_in_folder is not None:
            in_path_tmp = os.path.join(args.bbox_in_folder, clsname, modelname, 'models', 'model_normalized.obj')
            assert os.path.exists(in_path_tmp)
            mesh_tmp = trimesh.load(in_path_tmp, process=False)
            bbox = mesh_tmp.bounding_box.bounds
        else:
            bbox = mesh.bounding_box.bounds

        # Compute location and scale
        loc = (bbox[0] + bbox[1]) / 2
        scale = (bbox[1] - bbox[0]).max()

        # Transform input mesh
        mesh.apply_translation(-loc)
        mesh.apply_scale(1 / scale)

    # Export various modalities
    if args.pointcloud_folder is not None:
        filename = os.path.join(args.pointcloud_folder, clsname, modelname + '.npz')
        if not os.path.exists(os.path.dirname(filename)):
            os.mkdir(os.path.dirname(filename))
        export_pointcloud(mesh, filename, loc, scale, args)

    if args.voxels_folder is not None:
        if not os.path.exists(args.voxels_folder):
            os.mkdir(args.voxels_folder)
        export_voxels(mesh, args.voxels_folder, clsname, modelname, loc, scale, args)

    if args.points_folder is not None:
        filename = os.path.join(args.points_folder, clsname, modelname + '.npz')
        if not os.path.exists(os.path.dirname(filename)):
            os.mkdir(os.path.dirname(filename))
        export_points(mesh, filename, loc, scale, args)

    if args.mesh_folder is not None:
        filename = os.path.join(args.mesh_folder, clsname, modelname + '.off')
        if not os.path.exists(os.path.dirname(filename)):
            os.mkdir(os.path.dirname(filename))
        export_mesh(mesh, filename, loc, scale, args)

def main(args):
    input_files = glob(os.path.join(args.in_folder, '*', '*.off'))

    for file in input_files:
        process_path(file, args=args)

    # p = Pool(processes=args.n_proc)
    # p.map(partial(process_path, args=args), input_files)
    # p.close()
    # p.join()

if __name__ == '__main__':
    args = parse_args()
    if not os.path.isdir(args.pointcloud_folder):
        os.makedirs(args.pointcloud_folder)
    if not os.path.isdir(args.voxels_folder):
        os.makedirs(args.voxels_folder)
    if not os.path.isdir(args.points_folder):
        os.makedirs(args.points_folder)
    if not os.path.isdir(args.mesh_folder):
        os.makedirs(args.mesh_folder)
    main(args)