#!/usr/bin/env python3
"""
TIMR场景测试脚本 - 简化版
专门用于测试指定场景并生成BSP mesh
"""

import argparse
import imp
import torch
import yaml
import os
import sys
import numpy as np
from tqdm import tqdm

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型和数据
from model.maft.model.maft import MAFT
from data import build_dataset, build_dataloader
from utils.logger import get_logger
from model.maft.utils.mask_encoder import rle_decode
from utils.rfs_labels import RFS_CLASSES
from utils.util.bbox import BBoxUtils
from utils.consts import TIMR_TO_BSP_MAPPING
from utils.consts import CAD_labels as BSP_CLASS_NAMES

def filter_dataset_by_scene(dataset, scene_id):
    """过滤数据集，只保留指定场景"""
    filtered_data = []
    for data in dataset.data:
        if scene_id in data:
            filtered_data.append(data)
    
    if not filtered_data:
        raise ValueError(f"场景 {scene_id} 在数据集中未找到")
    
    dataset.data = filtered_data
    return dataset

def compute_mask_iou(mask1, mask2):
    """计算两个mask的IoU"""
    if isinstance(mask1, dict):
        mask1 = rle_decode(mask1)
    if isinstance(mask2, dict):
        mask2 = rle_decode(mask2)
    
    intersection = np.logical_and(mask1, mask2).sum()
    union = np.logical_or(mask1, mask2).sum()
    
    if union == 0:
        return 0.0
    return float(intersection) / float(union)

def compute_bbox_3d_iou(bbox1, bbox2):
    """计算两个3D bbox的IoU"""
    center1, size1 = bbox1[:3], bbox1[3:6]
    center2, size2 = bbox2[:3], bbox2[3:6]
    
    # 计算每个轴上的重合
    min1 = center1 - size1 / 2
    max1 = center1 + size1 / 2
    min2 = center2 - size2 / 2
    max2 = center2 + size2 / 2
    
    # 计算交集
    intersection_min = np.maximum(min1, min2)
    intersection_max = np.minimum(max1, max2)
    intersection_size = np.maximum(0, intersection_max - intersection_min)
    intersection_volume = np.prod(intersection_size)
    
    # 计算并集
    volume1 = np.prod(size1)
    volume2 = np.prod(size2)
    union_volume = volume1 + volume2 - intersection_volume
    
    if union_volume == 0:
        return 0.0
    return intersection_volume / union_volume

def deduplicate_instances(pred_instances, iou_threshold=0.5):
    """简化的去重函数 - 只对相同类别的实例进行去重"""
    if len(pred_instances) <= 1:
        return pred_instances
    
    print(f"开始去重: {len(pred_instances)} 个实例")
    
    # 按置信度降序排序
    sorted_instances = sorted(enumerate(pred_instances), 
                            key=lambda x: x[1].get('conf', 0.0), reverse=True)
    
    keep_instances = []
    
    for idx, (list_idx, inst) in enumerate(sorted_instances):
        should_keep = True
        current_label = inst.get('label_id', -1)
        
        # 检查与已保留的相同类别实例的重合度
        for keep_inst in keep_instances:
            keep_label = keep_inst.get('label_id', -1)
            
            # 只对相同类别的实例进行去重
            if current_label != keep_label:
                continue
            
            # 计算mask IoU
            mask_iou = 0.0
            if 'pred_mask' in inst and 'pred_mask' in keep_inst:
                try:
                    mask_iou = compute_mask_iou(inst['pred_mask'], keep_inst['pred_mask'])
                except:
                    mask_iou = 0.0
            
            # 计算bbox IoU
            bbox_iou = 0.0
            if 'bboxes' in inst and 'bboxes' in keep_inst:
                try:
                    bbox_iou = compute_bbox_3d_iou(inst['bboxes'], keep_inst['bboxes'])
                except:
                    bbox_iou = 0.0
            
            # 如果重合度过高，则去重
            if mask_iou > iou_threshold or bbox_iou > iou_threshold:
                should_keep = False
                break
        
        if should_keep:
            keep_instances.append(inst)
    
    print(f"去重完成: 保留 {len(keep_instances)} 个实例")
    return keep_instances

def generate_mesh(comp_net, pred_instances, scan_id, conf_threshold=0.5):
    """生成指定场景的mesh"""
    meshes = []
    
    # 过滤有效实例
    valid_instances = []
    for i, inst in enumerate(pred_instances):
        if 'pred_zs' not in inst or 'bboxes' not in inst or 'conf' not in inst:
            continue
        
        if inst['conf'] < conf_threshold:
            continue
        
        timr_label = inst['label_id']
        bsp_label = TIMR_TO_BSP_MAPPING.get(timr_label, -1)
        
        if bsp_label == -1:
            continue
        
        valid_instances.append((i, inst, bsp_label))
    
    if not valid_instances:
        print(f"场景 {scan_id}: 没有有效的实例用于mesh生成")
        return meshes
    
    print(f"场景 {scan_id}: 找到 {len(valid_instances)} 个有效实例用于mesh生成")
    
    # 准备批量数据
    batch_zs = []
    batch_bboxes = []
    batch_labels = []
    
    for i, (original_idx, inst, bsp_label) in enumerate(valid_instances):
        batch_zs.append(torch.from_numpy(inst['pred_zs']).float())
        
        # 将30维bbox转换为7维格式 [center(3) + size(3) + angle(1)]
        bbox_30d = inst['bboxes']  # shape: (30,)
        if len(bbox_30d) == 30:
            # 提取center和size (前6维)
            center_size = bbox_30d[:6]  # [cx, cy, cz, w, h, d]
            
            # 从角度分类和回归中计算最终角度
            angle_cls = bbox_30d[6:18]  # 12维角度分类
            angle_reg = bbox_30d[18:30]  # 12维角度回归
            
            # 使用BBoxUtils的class2angle方法正确转换角度
            bbox_utils = BBoxUtils(num_heading_bin=12)
            max_cls_idx = np.argmax(angle_cls)
            angle_residual = angle_reg[max_cls_idx]
            final_angle = bbox_utils.class2angle(max_cls_idx, angle_residual)
            
            
            # 组合成7维bbox
            bbox_7d = np.concatenate([center_size, [final_angle]])
        else:
            # 如果已经是7维或其他格式，直接使用前7维
            bbox_7d = bbox_30d[:7] if len(bbox_30d) >= 7 else bbox_30d
            
        batch_bboxes.append(torch.from_numpy(bbox_7d).float())
        batch_labels.append(torch.tensor(bsp_label).long())
    
    # 转换为batch tensor
    feats = torch.stack(batch_zs)
    bboxes = torch.stack(batch_bboxes)
    labels = torch.stack(batch_labels)
    
    # 移动到GPU
    if torch.cuda.is_available():
        feats = feats.cuda()
        bboxes = bboxes.cuda()
        labels = labels.cuda()
    
    # 准备数据字典
    data = {
        'feats': feats,
        'bboxes': bboxes,
        'labels': labels
    }
    
    try:
        # 调用BSP网络生成mesh
        print(f"正在调用BSP网络生成mesh...")
        with torch.no_grad():
            result = comp_net(data, phase=1, return_mesh='retrieve')
        
        # 处理生成的mesh
        if 'meshes' in result and len(result['meshes']) > 0:
            print(f"成功生成 {len(result['meshes'])} 个mesh")
            for j, (original_idx, inst, bsp_label) in enumerate(valid_instances):
                if j < len(result['meshes']):
                    mesh = result['meshes'][j]
                    bsp_class_name = BSP_CLASS_NAMES[bsp_label]
                    
                    mesh_data = {
                        'scan_id': scan_id,
                        'instance_id': original_idx,
                        'label_id': inst['label_id'],
                        'bsp_label_id': bsp_label,
                        'bsp_class_name': bsp_class_name,
                        'confidence': inst['conf'],
                        'mesh': mesh
                    }
                    meshes.append(mesh_data)
        else:
            print("BSP网络未返回有效的mesh")
    
    except Exception as e:
        print(f"BSP网络调用失败: {e}")
    
    return meshes

def generate_instance_color(instance_id, label_id, total_instances=None):
    """为实例生成独特的颜色"""
    import colorsys
    
    # 基于类别的基础色调
    category_hues = {
        3: 0.0,    # chair - 红色
        4: 0.1,    # table - 橙色
        5: 0.3,    # desk - 绿色
        6: 0.6,    # bed - 蓝色
        7: 0.8,    # bookshelf - 紫色
        8: 0.2,    # sofa - 黄绿色
        9: 0.4,    # sink - 青色
        10: 0.7,   # bathtub - 靛蓝色
        11: 0.9,   # toilet - 品红色
        12: 0.05,  # curtain - 红橙色
        13: 0.15,  # counter - 橙黄色
        14: 0.25,  # door - 黄色
        15: 0.35,  # window - 春绿色
        16: 0.45,  # shower curtain - 青绿色
        17: 0.55,  # refrigerator - 天蓝色
        18: 0.65,  # picture - 蓝紫色
        19: 0.75,  # cabinet - 紫蓝色
        20: 0.85,  # otherfurniture - 紫红色
    }
    
    # 获取基础色调
    base_hue = category_hues.get(label_id, (instance_id * 0.618033988749895) % 1.0)  # 黄金比例
    
    # 为同类别的不同实例添加色调偏移
    hue_offset = (instance_id * 0.1) % 0.2 - 0.1  # -0.1 到 0.1 的偏移
    final_hue = (base_hue + hue_offset) % 1.0
    
    # 高饱和度和亮度以获得鲜艳的颜色
    saturation = 0.8 + (instance_id % 3) * 0.1  # 0.8-1.0
    value = 0.7 + (instance_id % 4) * 0.075      # 0.7-0.925
    
    # 转换为RGB
    rgb = colorsys.hsv_to_rgb(final_hue, saturation, value)
    return [int(c * 255) for c in rgb]

def save_meshes(meshes, output_dir, scan_id):
    """保存mesh到文件，为每个实例分配不同颜色"""
    mesh_dir = os.path.join(output_dir, 'meshes', scan_id)
    os.makedirs(mesh_dir, exist_ok=True)
    
    saved_count = 0
    total_instances = len(meshes)
    
    for i, mesh_data in enumerate(meshes):
        try:
            mesh = mesh_data['mesh']
            instance_id = mesh_data['instance_id']
            label_id = mesh_data['label_id']
            bsp_class_name = mesh_data['bsp_class_name']
            confidence = mesh_data['confidence']
            
            # 为实例生成颜色
            color_rgb = generate_instance_color(instance_id, label_id, total_instances)
            
            # 直接生成颜色信息，不设置到mesh对象上
            print(f"  实例{instance_id} (类别{label_id}): 生成颜色 RGB{color_rgb}")
            
            # 生成文件名：场景_实例ID_TIMR类别_BSP类别_置信度.ply
            mesh_filename = f"{scan_id}_{instance_id:03d}_timr{label_id}_{bsp_class_name}_conf{confidence:.3f}.ply"
            mesh_path = os.path.join(mesh_dir, mesh_filename)
            
            # 保存带颜色的mesh
            if save_colored_mesh_as_ply(mesh, mesh_path, color_rgb):
                saved_count += 1
                print(f"  保存带颜色的mesh: {mesh_filename}")
            else:
                print(f"  保存mesh失败: {mesh_filename}")
            
        except Exception as e:
            print(f"保存mesh失败 (实例{mesh_data['instance_id']}): {e}")
            import traceback
            traceback.print_exc()
    
    print(f"成功保存 {saved_count}/{len(meshes)} 个带颜色的mesh到: {mesh_dir}")

def save_scene_pointcloud(cfg, scan_id, output_dir):
    """保存场景点云为PLY文件"""
    try:
        mesh_dir = os.path.join(output_dir, 'meshes', scan_id)
        os.makedirs(mesh_dir, exist_ok=True)
        
        # 从数据集配置获取数据路径
        data_root = cfg['data']['test']['data_root']
        prefix = cfg['data']['test']['prefix']
        suffix = cfg['data']['test']['suffix']
        
        # 构建场景数据文件路径
        scene_file = os.path.join(data_root, prefix, f'{scan_id}{suffix}')
        
        if not os.path.exists(scene_file):
            print(f"⚠️  场景数据文件不存在: {scene_file}")
            return
        
        print(f"📁 加载场景点云数据: {scene_file}")
        
        # 加载场景数据
        data = torch.load(scene_file, weights_only=False)
        
        # 获取点云坐标和颜色
        if 'locs_float' in data:
            coords = data['locs_float'].numpy()  # (N, 3) xyz坐标
        elif 'coord' in data:
            coords = data['coord'].numpy()
        else:
            print("❌ 未找到坐标数据")
            return
        
        # 获取颜色信息
        colors = None
        if 'feats' in data and data['feats'].shape[1] >= 3:
            # 前3个通道通常是颜色信息
            colors = data['feats'][:, :3].numpy()
            # 归一化到0-1范围
            if colors.max() > 1.0:
                colors = colors / 255.0
        
        # 保存为PLY文件
        ply_filename = f"{scan_id}_scene.ply"
        ply_path = os.path.join(mesh_dir, ply_filename)
        
        save_pointcloud_as_ply(coords, colors, ply_path)
        print(f"✅ 场景点云已保存: {ply_filename}")
        
    except Exception as e:
        print(f"❌ 保存场景点云失败: {e}")
        import traceback
        traceback.print_exc()

def save_origin_pointcloud(cfg, scan_id, output_dir):
    """保存原始点云到origin_cloud目录"""
    try:
        # 创建origin_cloud目录
        origin_cloud_dir = os.path.join(output_dir, 'origin_cloud')
        os.makedirs(origin_cloud_dir, exist_ok=True)
        
        # 构建原始点云文件路径
        data_root = cfg['data']['test'].get('data_root', 'datasets/scannet')
        data_file = os.path.join(data_root, 'processed_data', scan_id, 'data.npz')
        
        if not os.path.exists(data_file):
            print(f"⚠️  原始点云文件不存在: {data_file}")
            return
        
        print(f"📁 加载原始点云数据: {data_file}")
        
        # 读取原始点云数据
        data = np.load(data_file)
        if 'mesh_vertices' in data:
            mesh_vertices = data['mesh_vertices'].astype(np.float32)  # (N, 6) - XYZRGB
            
            # 分离坐标和颜色
            coords = mesh_vertices[:, :3]  # XYZ坐标
            colors = mesh_vertices[:, 3:6]  # RGB颜色
            
            # 归一化颜色到0-1范围
            if colors.max() > 1.0:
                colors = colors / 255.0
            
            # 保存为PLY格式
            ply_filename = f"{scan_id}_origin.ply"
            ply_path = os.path.join(origin_cloud_dir, ply_filename)
            
            save_pointcloud_as_ply(coords, colors, ply_path)
            print(f"✅ 原始点云已保存到: origin_cloud/{ply_filename}")
        else:
            print(f"⚠️  data.npz中没有找到mesh_vertices字段")
        
    except Exception as e:
        print(f"❌ 保存原始点云失败: {e}")
        import traceback
        traceback.print_exc()

def save_colored_mesh_as_ply(mesh, output_path, color_rgb=None):
    """使用open3d将带颜色的mesh保存为PLY文件"""
    try:
        import open3d as o3d
        
        # 创建open3d mesh对象
        o3d_mesh = o3d.geometry.TriangleMesh()
        o3d_mesh.vertices = o3d.utility.Vector3dVector(mesh.vertices)
        o3d_mesh.triangles = o3d.utility.Vector3iVector(mesh.faces)
        
        # 设置颜色
        if color_rgb is not None:
            # 使用传入的RGB颜色为所有顶点着色
            r, g, b = color_rgb
            # open3d需要0-1范围的颜色
            vertex_colors = np.full((len(mesh.vertices), 3), [r/255.0, g/255.0, b/255.0], dtype=np.float64)
            o3d_mesh.vertex_colors = o3d.utility.Vector3dVector(vertex_colors)
        else:
            # 尝试从mesh对象获取颜色
            vertex_colors = None
            if hasattr(mesh, 'visual') and hasattr(mesh.visual, 'vertex_colors'):
                vertex_colors = mesh.visual.vertex_colors
            elif hasattr(mesh, 'vertex_colors'):
                vertex_colors = mesh.vertex_colors
            
            if vertex_colors is not None:
                # 确保颜色格式正确
                if vertex_colors.shape[1] == 4:  # RGBA
                    vertex_colors = vertex_colors[:, :3]  # 只取RGB
                
                # 确保颜色在0-1范围内
                if vertex_colors.max() > 1.0:
                    vertex_colors = vertex_colors / 255.0
                
                o3d_mesh.vertex_colors = o3d.utility.Vector3dVector(vertex_colors.astype(np.float64))
            else:
                # 使用默认灰色
                vertex_colors = np.full((len(mesh.vertices), 3), [0.5, 0.5, 0.5], dtype=np.float64)
                o3d_mesh.vertex_colors = o3d.utility.Vector3dVector(vertex_colors)
        
        # 保存PLY文件
        success = o3d.io.write_triangle_mesh(output_path, o3d_mesh)
        
        if not success:
            print(f"open3d保存失败，尝试fallback方法")
            return save_colored_mesh_as_ply_fallback(mesh, output_path, color_rgb)
        
        return True
        
    except ImportError:
        print(f"open3d未安装，使用fallback方法")
        return save_colored_mesh_as_ply_fallback(mesh, output_path, color_rgb)
    except Exception as e:
        print(f"open3d保存失败: {e}，尝试fallback方法")
        return save_colored_mesh_as_ply_fallback(mesh, output_path, color_rgb)

def save_colored_mesh_as_ply_fallback(mesh, output_path, color_rgb=None):
    """fallback方法：手动写入PLY文件"""
    try:
        vertices = mesh.vertices
        faces = mesh.faces
        
        # 使用传入的颜色或默认颜色
        if color_rgb is not None:
            # 使用传入的RGB颜色为所有顶点着色
            r, g, b = color_rgb
            vertex_colors = np.full((len(vertices), 3), [r, g, b], dtype=np.uint8)
        else:
            # 尝试从mesh对象获取颜色
            vertex_colors = None
            if hasattr(mesh, 'visual') and hasattr(mesh.visual, 'vertex_colors'):
                vertex_colors = mesh.visual.vertex_colors
            elif hasattr(mesh, 'vertex_colors'):
                vertex_colors = mesh.vertex_colors
            
            # 如果没有颜色，使用默认灰色
            if vertex_colors is None:
                vertex_colors = np.full((len(vertices), 3), [128, 128, 128], dtype=np.uint8)
            else:
                # 确保颜色格式正确
                if vertex_colors.shape[1] == 4:  # RGBA
                    vertex_colors = vertex_colors[:, :3]  # 只取RGB
                
                # 确保颜色在0-255范围内
                if vertex_colors.max() <= 1.0:
                    vertex_colors = (vertex_colors * 255).astype(np.uint8)
                else:
                    vertex_colors = vertex_colors.astype(np.uint8)
        
        # 写入PLY文件
        with open(output_path, 'w') as f:
            f.write('ply\n')
            f.write('format ascii 1.0\n')
            f.write(f'element vertex {len(vertices)}\n')
            f.write('property float x\n')
            f.write('property float y\n')
            f.write('property float z\n')
            f.write('property uchar red\n')
            f.write('property uchar green\n')
            f.write('property uchar blue\n')
            f.write(f'element face {len(faces)}\n')
            f.write('property list uchar int vertex_index\n')
            f.write('end_header\n')
            
            # 写入顶点数据
            for i, vertex in enumerate(vertices):
                x, y, z = vertex
                r, g, b = vertex_colors[i]
                f.write(f'{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n')
            
            # 写入面数据
            for face in faces:
                f.write(f'3 {face[0]} {face[1]} {face[2]}\n')
        
        return True
        
    except Exception as e:
        print(f"保存带颜色的mesh失败: {e}")
        return False

def save_pointcloud_as_ply(xyz, colors, output_path):
    """将点云保存为PLY文件"""
    try:
        num_points = len(xyz)
        
        # 如果没有颜色，使用默认灰色
        if colors is None:
            colors = np.ones((num_points, 3)) * 0.7
        
        # 确保颜色在0-255范围内
        if colors.max() <= 1.0:
            colors = (colors * 255).astype(np.uint8)
        else:
            colors = colors.astype(np.uint8)
        
        # 写入PLY文件
        with open(output_path, 'w') as f:
            f.write('ply\n')
            f.write('format ascii 1.0\n')
            f.write(f'element vertex {num_points}\n')
            f.write('property float x\n')
            f.write('property float y\n') 
            f.write('property float z\n')
            f.write('property uchar red\n')
            f.write('property uchar green\n')
            f.write('property uchar blue\n')
            f.write('end_header\n')
            
            for i in range(num_points):
                x, y, z = xyz[i]
                r, g, b = colors[i]
                f.write(f'{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n')
        
        return True
        
    except Exception as e:
        print(f"保存PLY文件失败: {e}")
        return False

def save_bbox_info(output_dir, scan_id, pred_instances, suffix="", conf_threshold=0.5):
    """保存bbox信息到txt文件"""
    scene_dir = os.path.join(output_dir, 'scenes', scan_id)
    os.makedirs(scene_dir, exist_ok=True)
    
    bbox_file = os.path.join(scene_dir, f'{scan_id}_bboxes{suffix}.txt')
    
    with open(bbox_file, 'w', encoding='utf-8') as f:
        f.write("# instance_id label_id confidence cx cy cz w h d\n")
        
        for i, inst in enumerate(pred_instances):
            label_id = inst['label_id']
            confidence = inst.get('conf', 0.0)
            
            if 'bboxes' in inst and len(inst['bboxes']) >= 6:
                bbox = inst['bboxes']
                cx, cy, cz = bbox[0], bbox[1], bbox[2]
                w, h, d = bbox[3], bbox[4], bbox[5]
                
                f.write(f"{i:03d} {label_id:2d} {confidence:.4f} ")
                f.write(f"{cx:8.4f} {cy:8.4f} {cz:8.4f} ")
                f.write(f"{w:7.4f} {h:7.4f} {d:7.4f}\n")
    
    print(f"bbox信息已保存: {bbox_file}")

def get_args():
    parser = argparse.ArgumentParser('TIMR Scene Mesh Test')
    parser.add_argument('config', type=str, help='path to config file')
    parser.add_argument('checkpoint', type=str, help='path to checkpoint')
    parser.add_argument('--scene', type=str, required=True, help='scene name to test (e.g., scene0011_00)')
    parser.add_argument('--out', type=str, required=True, help='directory for output results')
    parser.add_argument('--gpu', type=int, default=0, help='GPU device to use')
    parser.add_argument('--conf-threshold', type=float, default=0.5, help='confidence threshold for mesh generation')
    parser.add_argument('--save-masks', action='store_true', help='save instance masks')
    parser.add_argument('--save-bbox', action='store_true', default=True, help='save bbox information to txt files')
    
    # 去重参数
    parser.add_argument('--enable-dedup', action='store_true', help='enable instance deduplication (default: False)')
    parser.add_argument('--disable-dedup', action='store_true', help='disable instance deduplication')
    parser.add_argument('--dedup-iou-threshold', type=float, default=0.5, help='IoU threshold for deduplication (default: 0.5)')
    
    args = parser.parse_args()
    return args

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def filter_dataset_by_scene(dataset, target_scene):
    """过滤数据集，只保留指定场景"""
    filtered_files = []
    
    for file_path in dataset.filenames:
        # 从文件路径提取场景名
        # 路径格式：/path/to/processed_data/scene0011_00/data.npz -> scene0011_00
        scene_name = os.path.basename(os.path.dirname(file_path))
        if scene_name == target_scene:
            filtered_files.append(file_path)
    
    if not filtered_files:
        raise ValueError(f"未找到场景 '{target_scene}' 的数据文件")
    
    # 更新数据集的文件列表
    dataset.filenames = filtered_files
    print(f"找到场景 '{target_scene}' 的数据文件: {len(filtered_files)} 个")
    
    return dataset

def main():
    args = get_args()
    
    # 设置GPU
    if torch.cuda.is_available():
        torch.cuda.set_device(args.gpu)
        print(f"使用GPU: cuda:{args.gpu}")
    else:
        print("使用CPU")
    
    # 创建输出目录
    os.makedirs(args.out, exist_ok=True)
    
    # 加载配置
    cfg = load_config(args.config)
    
    # 设置随机种子
    torch.manual_seed(cfg['test']['seed'])
    
    # 设置日志
    logger = get_logger('TIMR-Scene-Mesh-Test')
    logger.info(f"测试场景: {args.scene}")
    logger.info(f"置信度阈值: {args.conf_threshold}")
    logger.info(f"输出目录: {args.out}")
    
    # 构建模型
    logger.info("构建TIMR模型...")
    model_config = cfg['model'].copy()
    model_config.pop('name', None)  # 移除name字段
    model = MAFT(**model_config)
    model = model.cuda() if torch.cuda.is_available() else model
    
    # 加载检查点
    logger.info(f'加载模型权重: {args.checkpoint}')
    checkpoint = torch.load(args.checkpoint, map_location='cpu', weights_only=False)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)
    
    # 构建数据集并过滤指定场景
    logger.info("构建测试数据集...")
    dataset = build_dataset(cfg['data']['test'], logger)
    dataset = filter_dataset_by_scene(dataset, args.scene)
    
    # 构建数据加载器
    dataloader = build_dataloader(dataset, training=False, **cfg['dataloader']['test'])
    logger.info(f"数据加载器准备完成，批次数: {len(dataloader)}")
    
    # 开始测试
    logger.info("开始测试...")
    all_meshes = []
    results = []
    
    with torch.no_grad():
        model.eval()
        
        for batch_idx, batch in enumerate(tqdm(dataloader, desc="处理批次")):
            # 数据移动到GPU
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    batch[key] = value.cuda() if torch.cuda.is_available() else value
            
            # 模型推理
            result = model(batch, mode='predict')
            results.append(result)
            
            # 获取场景ID
            scan_id = result.get('scan_id', args.scene)
            logger.info(f"处理场景: {scan_id}")
            
            # 保存原始点云到origin_cloud目录
            logger.info("保存原始点云...")
            save_origin_pointcloud(cfg, scan_id, args.out)
            
            # 保存输入场景的点云
            logger.info("保存输入场景点云...")
            save_scene_pointcloud(cfg, scan_id, args.out)
            
            # 检查预测实例
            if 'pred_instances' in result and len(result['pred_instances']) > 0:
                pred_instances = result['pred_instances']
                logger.info(f"找到 {len(pred_instances)} 个预测实例")
                
                # 打印实例信息
                for i, inst in enumerate(pred_instances):
                    label_id = inst.get('label_id', -1)
                    conf = inst.get('conf', 0.0)
                    class_name = RFS_CLASSES[label_id] if label_id < len(RFS_CLASSES) else f"class_{label_id}"
                    logger.info(f"  实例 {i}: {class_name} (ID:{label_id}, 置信度:{conf:.3f})")
                
                # 去重实例（根据参数决定）
                should_dedup = args.enable_dedup and not args.disable_dedup
                if should_dedup:
                    logger.info("开始去重实例...")
                    deduplicated_instances = deduplicate_instances(pred_instances, iou_threshold=args.dedup_iou_threshold)
                    logger.info(f"去重前: {len(pred_instances)} 个实例，去重后: {len(deduplicated_instances)} 个实例")
                else:
                    logger.info("跳过去重步骤")
                    deduplicated_instances = pred_instances
                
                # 保存bbox信息
                if args.save_bbox:
                    save_bbox_info(args.out, scan_id, deduplicated_instances, conf_threshold=args.conf_threshold)
                
                # 生成mesh
                logger.info("开始生成mesh...")
                meshes = generate_mesh(model.decoder.comp_net, deduplicated_instances, scan_id, args.conf_threshold)
                
                if meshes:
                    logger.info(f"成功生成 {len(meshes)} 个mesh")
                    all_meshes.extend(meshes)
                    save_meshes(meshes, args.out, scan_id)
                else:
                    logger.warning("未生成任何mesh")
            else:
                logger.warning("未找到预测实例")
    
    # 输出最终统计
    logger.info("=== 测试完成 ===")
    logger.info(f"处理场景数: {len(results)}")
    logger.info(f"总生成mesh数: {len(all_meshes)}")
    
    # 保存简单总结
    summary_file = os.path.join(args.out, 'test_summary.txt')
    with open(summary_file, 'w') as f:
        f.write(f"TIMR Scene Mesh Test Summary\n")
        f.write(f"Scene: {args.scene}\n")
        f.write(f"Total meshes generated: {len(all_meshes)}\n")
        f.write(f"Output directory: {args.out}\n")
    
    logger.info(f"测试总结已保存到: {summary_file}")
    logger.info("测试完成！")

if __name__ == '__main__':
    main()