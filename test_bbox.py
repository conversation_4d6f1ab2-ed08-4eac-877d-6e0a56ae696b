#!/usr/bin/env python3
"""
TIMR BBox测试脚本
用于生成bbox_iou评估所需的.npy格式文件
基于test.py改编，专门用于bbox预测和保存
"""

import argparse
import torch
import yaml
import os
import sys
import numpy as np
from tqdm import tqdm

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型和数据
from model.maft.model.maft import MAFT
from data import build_dataset, build_dataloader
from utils.logger import get_logger
from utils.consts import TIMR_TO_BSP_MAPPING, CAD2ShapeNetID

# 导入RFS标签系统
from utils.rfs_labels import RFS_CLASSES

# 对应的NYU_ID映射
RFS_NYU_ID = (
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 24, 28, 33, 34, 36, 39,  # NYU20
    -1, -1, -1, -1, -1  # 新增的5个类别没有NYU_ID对应
)

def extract_bboxes_from_instances(pred_instances, scan_id):
    """
    从预测实例中提取bbox信息并转换为评估所需格式
    
    Args:
        pred_instances: 预测实例列表
        scan_id: 场景ID
        
    Returns:
        numpy.ndarray: shape为(N, 9)的数组，每行为[x,y,z,w,h,d,angle,score,label]
    """
    from utils.util.bbox import BBoxUtils
    
    bboxes_list = []
    bbox_utils = BBoxUtils(num_heading_bin=12)
    
    for inst in pred_instances:
        # 检查必要字段
        if 'bboxes' not in inst or 'label_id' not in inst or 'conf' not in inst:
            continue
            
        # 获取TIMR预测的类别ID
        timr_label = inst['label_id']
        
        # 通过TIMR_TO_BSP_MAPPING转换为CAD索引ID
        bsp_label = TIMR_TO_BSP_MAPPING.get(timr_label, -1)
        
        # 跳过不支持的类别
        if bsp_label == -1:
            continue
            
        # 获取bbox数据并转换为7维格式
        bbox_30d = inst['bboxes']  # shape: (30,)
        
        if len(bbox_30d) == 30:
            # 将30维bbox转换为7维格式 [center(3) + size(3) + angle(1)]
            # 提取center和size (前6维)
            center_size = bbox_30d[:6]  # [cx, cy, cz, w, h, d]
            
            # 从角度分类和回归中计算最终角度
            angle_cls = bbox_30d[6:18]  # 12维角度分类
            angle_reg = bbox_30d[18:30]  # 12维角度回归
            
            # 使用BBoxUtils的class2angle方法正确转换角度
            max_cls_idx = np.argmax(angle_cls)
            angle_residual = angle_reg[max_cls_idx]
            final_angle = bbox_utils.class2angle(max_cls_idx, angle_residual)
            
            # 组合成7维bbox
            bbox_coords = np.concatenate([center_size, [final_angle]])
        else:
            # 如果已经是7维或其他格式，直接使用前7维
            bbox_coords = bbox_30d[:7] if len(bbox_30d) >= 7 else bbox_30d
        
        # 获取置信度分数
        confidence = inst['conf']
        
        # 转换为CAD2ShapeNetID的ID
        shapenet_id = int(CAD2ShapeNetID[bsp_label])
        
        # 组合为最终格式: [x,y,z,w,h,d,angle,score,label]
        bbox_row = np.concatenate([
            bbox_coords,  # [x,y,z,w,h,d,angle] - 7维
            [confidence],  # score - 1维
            [shapenet_id]  # label - 1维
        ])
        
        bboxes_list.append(bbox_row)
    
    # 如果没有有效的bbox，返回空数组
    if len(bboxes_list) == 0:
        return np.empty((0, 9), dtype=np.float32)
    
    # 转换为numpy数组
    bboxes_array = np.stack(bboxes_list, axis=0).astype(np.float32)
    
    return bboxes_array

def save_bbox_predictions(output_dir, scan_id, bboxes_array):
    """
    保存bbox预测结果为.npy文件
    
    Args:
        output_dir: 输出目录
        scan_id: 场景ID
        bboxes_array: bbox数组，shape为(N, 9)
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存为.npy文件
    output_path = os.path.join(output_dir, f'{scan_id}.npy')
    np.save(output_path, bboxes_array)
    
    return output_path

def get_args():
    parser = argparse.ArgumentParser('TIMR BBox Test')
    parser.add_argument('config', type=str, help='path to config file')
    parser.add_argument('checkpoint', type=str, help='path to checkpoint')
    parser.add_argument('--out', type=str, required=True, help='directory for output bbox results')
    parser.add_argument('--gpu', type=int, default=0, help='GPU device to use')
    parser.add_argument('--debug', action='store_true', help='enable debug mode (only test first batch)')
    parser.add_argument('--conf-threshold', type=float, default=0.0, 
                        help='confidence threshold for bbox filtering (default: 0.0)')
    args = parser.parse_args()
    return args

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def get_dataset_info(dataset):
    """获取数据集的类别和NYU_ID信息"""
    classes = getattr(dataset, 'CLASSES', RFS_CLASSES)
    nyu_id = getattr(dataset, 'NYU_ID', RFS_NYU_ID)
    return classes, nyu_id

def main():
    args = get_args()
    
    # 设置GPU
    if torch.cuda.is_available():
        torch.cuda.set_device(args.gpu)
    
    # 加载配置
    cfg = load_config(args.config)
    
    # 设置随机种子
    torch.manual_seed(cfg['test']['seed'])
    
    # 设置日志
    logger = get_logger('TIMR-BBox-Test')
    logger.info(f"Using device: cuda:{args.gpu}" if torch.cuda.is_available() else "cpu")
    logger.info(f"Confidence threshold: {args.conf_threshold}")
    logger.info(f"Output directory: {args.out}")
    
    # 构建模型
    logger.info("Building TIMR model...")
    model_config = cfg['model'].copy()
    model_config.pop('name', None)
    model = MAFT(**model_config)
    model = model.cuda() if torch.cuda.is_available() else model
    
    # 加载检查点
    logger.info(f'Load checkpoint from {args.checkpoint}')
    checkpoint = torch.load(args.checkpoint, map_location='cpu', weights_only=False)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)
    
    # 构建数据加载器
    logger.info("Building test data loader...")
    dataset = build_dataset(cfg['data']['test'], logger)
    dataloader = build_dataloader(dataset, training=False, **cfg['dataloader']['test'])
    
    # 获取数据集信息
    classes, nyu_id = get_dataset_info(dataset)
    logger.info(f"Dataset classes: {len(classes)} classes")
    logger.info(f"Test samples: {len(dataset)}")
    
    # 打印映射信息
    logger.info("=== 类别映射信息 ===")
    logger.info(f"TIMR_TO_BSP_MAPPING: {TIMR_TO_BSP_MAPPING}")
    logger.info(f"CAD2ShapeNetID: {CAD2ShapeNetID}")
    
    # 开始测试
    results = []
    scan_ids = []
    total_bboxes = 0
    total_scenes = 0
    
    progress_bar = tqdm(total=len(dataloader), desc="Processing scenes")
    
    with torch.no_grad():
        model.eval()
        
        for batch_idx, batch in enumerate(dataloader):
            # 数据移动到GPU
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    batch[key] = value.cuda() if torch.cuda.is_available() else value
            
            # 模型推理
            result = model(batch, mode='predict')
            
            # 获取场景ID
            scan_id = result.get('scan_id', f'scene_{batch_idx:06d}')
            scan_ids.append(scan_id)
            
            # 提取bbox信息
            if 'pred_instances' in result and len(result['pred_instances']) > 0:
                # 过滤低置信度的实例
                filtered_instances = []
                for inst in result['pred_instances']:
                    if inst.get('conf', 0.0) >= args.conf_threshold:
                        filtered_instances.append(inst)
                
                # 提取bbox
                bboxes_array = extract_bboxes_from_instances(filtered_instances, scan_id)
                
                # 保存bbox预测结果
                if len(bboxes_array) > 0:
                    output_path = save_bbox_predictions(args.out, scan_id, bboxes_array)
                    logger.debug(f"Saved {len(bboxes_array)} bboxes for {scan_id} to {output_path}")
                    total_bboxes += len(bboxes_array)
                else:
                    # 即使没有bbox也要保存空文件
                    output_path = save_bbox_predictions(args.out, scan_id, bboxes_array)
                    logger.debug(f"Saved empty bbox file for {scan_id}")
            else:
                # 没有预测实例，保存空文件
                empty_bboxes = np.empty((0, 9), dtype=np.float32)
                output_path = save_bbox_predictions(args.out, scan_id, empty_bboxes)
                logger.debug(f"No predictions for {scan_id}, saved empty file")
            
            total_scenes += 1
            progress_bar.update()
            
            # 调试模式只处理第一个batch
            if args.debug:
                logger.info("Debug mode enabled, processing only first batch")
                break
    
    progress_bar.close()
    
    # 统计信息
    logger.info("=== BBox测试结果统计 ===")
    logger.info(f"总场景数: {total_scenes}")
    logger.info(f"总bbox数: {total_bboxes}")
    logger.info(f"平均每场景bbox数: {total_bboxes/total_scenes:.2f}")
    logger.info(f"置信度阈值: {args.conf_threshold}")
    
    # 保存统计信息
    summary_path = os.path.join(args.out, 'bbox_test_summary.txt')
    with open(summary_path, 'w') as f:
        f.write("TIMR BBox Test Results\n")
        f.write("=" * 30 + "\n")
        f.write(f"Total scenes: {total_scenes}\n")
        f.write(f"Total bboxes: {total_bboxes}\n")
        f.write(f"Average bboxes per scene: {total_bboxes/total_scenes:.2f}\n")
        f.write(f"Confidence threshold: {args.conf_threshold}\n")
        f.write(f"Output directory: {args.out}\n")
        f.write(f"Classes mapping: TIMR -> BSP -> ShapeNetID\n")
        f.write(f"TIMR_TO_BSP_MAPPING: {TIMR_TO_BSP_MAPPING}\n")
        f.write(f"CAD2ShapeNetID: {CAD2ShapeNetID}\n")
    
    logger.info(f"统计信息已保存到: {summary_path}")
    logger.info(f"BBox预测结果已保存到: {args.out}")
    logger.info("BBox测试完成！")
    
    # 验证输出格式
    logger.info("=== 验证输出格式 ===")
    sample_files = [f for f in os.listdir(args.out) if f.endswith('.npy')]
    if sample_files:
        sample_file = sample_files[0]
        sample_path = os.path.join(args.out, sample_file)
        sample_data = np.load(sample_path)
        logger.info(f"示例文件: {sample_file}")
        logger.info(f"数据形状: {sample_data.shape}")
        if len(sample_data) > 0:
            logger.info(f"数据类型: {sample_data.dtype}")
            logger.info(f"第一行数据: {sample_data[0]}")
            logger.info("格式说明: [x,y,z,w,h,d,angle,score,shapenet_id]")
        else:
            logger.info("空文件（该场景无有效预测）")
    else:
        logger.warning("未找到输出的.npy文件")

if __name__ == '__main__':
    main()