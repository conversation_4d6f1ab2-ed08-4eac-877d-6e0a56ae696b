#!/usr/bin/env python3
"""
TIMR测试结果可视化脚本 - 基于QIMR版本适配
支持可视化原始点云、GT实例分割和预测实例分割结果
"""

import argparse
import numpy as np
import os
import os.path as osp
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# yapf:disable
COLOR_DETECTRON2 = np.array(
    [
        0.000, 0.447, 0.741,
        0.850, 0.325, 0.098,
        0.929, 0.694, 0.125,
        0.494, 0.184, 0.556,
        0.466, 0.674, 0.188,
        0.301, 0.745, 0.933,
        0.635, 0.078, 0.184,
        # 0.300, 0.300, 0.300,
        0.600, 0.600, 0.600,
        1.000, 0.000, 0.000,
        1.000, 0.500, 0.000,
        0.749, 0.749, 0.000,
        0.000, 1.000, 0.000,
        0.000, 0.000, 1.000,
        0.667, 0.000, 1.000,
        0.333, 0.333, 0.000,
        0.333, 0.667, 0.000,
        0.333, 1.000, 0.000,
        0.667, 0.333, 0.000,
        0.667, 0.667, 0.000,
        0.667, 1.000, 0.000,
        1.000, 0.333, 0.000,
        1.000, 0.667, 0.000,
        1.000, 1.000, 0.000,
        0.000, 0.333, 0.500,
        0.000, 0.667, 0.500,
        0.000, 1.000, 0.500,
        0.333, 0.000, 0.500,
        0.333, 0.333, 0.500,
        0.333, 0.667, 0.500,
        0.333, 1.000, 0.500,
        0.667, 0.000, 0.500,
        0.667, 0.333, 0.500,
        0.667, 0.667, 0.500,
        0.667, 1.000, 0.500,
        1.000, 0.000, 0.500,
        1.000, 0.333, 0.500,
        1.000, 0.667, 0.500,
        1.000, 1.000, 0.500,
        0.000, 0.333, 1.000,
        0.000, 0.667, 1.000,
        0.000, 1.000, 1.000,
        0.333, 0.000, 1.000,
        0.333, 0.333, 1.000,
        0.333, 0.667, 1.000,
        0.333, 1.000, 1.000,
        0.667, 0.000, 1.000,
        0.667, 0.333, 1.000,
        0.667, 0.667, 1.000,
        0.667, 1.000, 1.000,
        1.000, 0.000, 1.000,
        1.000, 0.333, 1.000,
        1.000, 0.667, 1.000,
        # 0.333, 0.000, 0.000,
        0.500, 0.000, 0.000,
        0.667, 0.000, 0.000,
        0.833, 0.000, 0.000,
        1.000, 0.000, 0.000,
        0.000, 0.167, 0.000,
        # 0.000, 0.333, 0.000,
        0.000, 0.500, 0.000,
        0.000, 0.667, 0.000,
        0.000, 0.833, 0.000,
        0.000, 1.000, 0.000,
        0.000, 0.000, 0.167,
        # 0.000, 0.000, 0.333,
        0.000, 0.000, 0.500,
        0.000, 0.000, 0.667,
        0.000, 0.000, 0.833,
        0.000, 0.000, 1.000,
        # 0.000, 0.000, 0.000,
        0.143, 0.143, 0.143,
        0.857, 0.857, 0.857,
        # 1.000, 1.000, 1.000
    ]).astype(np.float32).reshape(-1, 3) * 255
# yapf:enable


def get_coords_color(opt):
    """获取坐标和颜色数据 - 完全按照QIMR的逻辑"""
    
    # 加载原始数据 - 从TIMR数据格式加载
    data_file = osp.join(opt.data_root, 'processed_data', opt.room_name, 'data.npz')
    if not osp.exists(data_file):
        raise FileNotFoundError(f"Scene data not found: {data_file}")
    
    data = np.load(data_file)
    mesh_vertices = data['mesh_vertices']  # (N, 6) - XYZRGB
    semantic_labels = data['semantic_labels']  # (N,)
    instance_labels = data['instance_labels']  # (N,)
    
    xyz = mesh_vertices[:, :3].astype(np.float32)
    rgb = mesh_vertices[:, 3:6].astype(np.float32)
    data.close()
    
    # 按照QIMR的逻辑处理RGB
    rgb = rgb / 255.0 * 2 - 1  # 归一化到[-1,1]再转回[0,255]
    rgb = (rgb + 1) * 127.5

    # same color order according to instance pointnum
    if opt.task == 'instance_gt':
        inst_label = instance_labels.astype(int)
        print('Instance number: {}'.format(inst_label.max() + 1))
        inst_label_rgb = np.zeros(rgb.shape)
        ins_num = inst_label.max() + 1
        ins_pointnum = np.zeros(ins_num)
        for _ins_id in range(ins_num):
            ins_pointnum[_ins_id] = (inst_label == _ins_id).sum()
        sort_idx = np.argsort(ins_pointnum)[::-1]
        for _sort_id in range(ins_num):
            inst_label_rgb[inst_label == sort_idx[_sort_id]] = COLOR_DETECTRON2[_sort_id % len(COLOR_DETECTRON2)]
        rgb = inst_label_rgb

    # same color order according to instance pointnum
    elif opt.task == 'instance_pred':
        instance_file = os.path.join(opt.prediction_path, 'pred_instance', opt.room_name + '.txt')
        assert os.path.isfile(instance_file), 'No instance result - {}.'.format(instance_file)
        f = open(instance_file, 'r')
        masks = f.readlines()
        masks = [mask.rstrip().split() for mask in masks]
        inst_label_pred_rgb = np.zeros(rgb.shape)  # np.ones(rgb.shape) * 255 #

        ins_num = len(masks)
        ins_pointnum = np.zeros(ins_num)
        inst_label = -100 * np.ones(rgb.shape[0]).astype(int)

        # sort score such that high score has high priority for visualization
        scores = np.array([float(x[-1]) for x in masks])
        sort_inds = np.argsort(scores)[::-1]
        valid_instances = 0
        for i_ in range(len(masks) - 1, -1, -1):
            i = sort_inds[i_]
            mask_path = os.path.join(opt.prediction_path, 'pred_instance', masks[i][0])
            assert os.path.isfile(mask_path), mask_path
            confidence = float(masks[i][2])
            if confidence < opt.conf_thresh:
                continue
            mask = np.array(open(mask_path).read().splitlines(), dtype=int)
            print('{} {}: pointnum: {}, conf: {:.4f}'.format(i, masks[i], mask.sum(), confidence))
            ins_pointnum[i] = mask.sum()
            inst_label[mask == 1] = i
            valid_instances += 1
        sort_idx = np.argsort(ins_pointnum)[::-1]
        for _sort_id in range(ins_num):
            inst_label_pred_rgb[inst_label == sort_idx[_sort_id]] = COLOR_DETECTRON2[_sort_id % len(COLOR_DETECTRON2)]
        rgb = inst_label_pred_rgb
        print(f'Total instances: {ins_num}, Valid instances (conf >= {opt.conf_thresh}): {valid_instances}')

    elif opt.task == 'origin_pc':
        pass

#    sem_valid = (label != -100)
#    xyz = xyz[sem_valid]
#    rgb = rgb[sem_valid]

    return xyz, rgb


def write_ply(verts, colors, indices, output_file):
    if colors is None:
        colors = np.zeros_like(verts)
    if indices is None:
        indices = []

    file = open(output_file, 'w')
    file.write('ply \n')
    file.write('format ascii 1.0\n')
    file.write('element vertex {:d}\n'.format(len(verts)))
    file.write('property float x\n')
    file.write('property float y\n')
    file.write('property float z\n')
    file.write('property uchar red\n')
    file.write('property uchar green\n')
    file.write('property uchar blue\n')
    file.write('element face {:d}\n'.format(len(indices)))
    file.write('property list uchar uint vertex_indices\n')
    file.write('end_header\n')
    for vert, color in zip(verts, colors):
        file.write('{:f} {:f} {:f} {:d} {:d} {:d}\n'.format(vert[0], vert[1], vert[2], int(color[0]),
                                                            int(color[1]), int(color[2])))
    for ind in indices:
        file.write('3 {:d} {:d} {:d}\n'.format(ind[0], ind[1], ind[2]))
    file.close()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--data_root', help='path to the TIMR dataset root', required=True)
    parser.add_argument('--prediction_path', help='path to the prediction results', default='./results')
    parser.add_argument('--room_name', help='room_name', default='scene0011_00')
    parser.add_argument('--task', help='instance_gt/instance_pred/origin_pc', default='instance_pred')
    parser.add_argument('--out', help='output point cloud file in FILE.ply format')
    parser.add_argument('--conf_thresh', type=float, default=0.3, help='confidence threshold for predicted instances')
    opt = parser.parse_args()

    xyz, rgb = get_coords_color(opt)
    points = xyz[:, :3]
    colors = rgb / 255

    if opt.out:
        assert '.ply' in opt.out, 'output cloud file should be in FILE.ply format'
        write_ply(points, colors * 255, None, opt.out)
        print(f"Point cloud saved to: {opt.out}")
    else:
        try:
            import open3d as o3d
            pc = o3d.geometry.PointCloud()
            pc.points = o3d.utility.Vector3dVector(points)
            pc.colors = o3d.utility.Vector3dVector(colors)

            vis = o3d.visualization.Visualizer()
            vis.create_window()
            vis.add_geometry(pc)
            vis.get_render_option().point_size = 1.5
            vis.run()
            vis.destroy_window()
        except:
            print("无法创建可视化窗口，请使用 --out 参数保存为PLY文件")


if __name__ == '__main__':
    exit(main()) 