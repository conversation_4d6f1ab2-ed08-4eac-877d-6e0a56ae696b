from datetime import datetime
import logging
import os

import yaml


def update_recursive(dict1, dict2):
    ''' Update two config dictionaries recursively.

    Args:
        dict1 (dict): first dictionary to be updated
        dict2 (dict): second dictionary which entries should be used

    '''
    for k, v in dict2.items():
        if k not in dict1:
            dict1[k] = dict()
        if isinstance(v, dict):
            update_recursive(dict1[k], v)
        else:
            dict1[k] = v


class CONFIG(object):
    def __init__(self, input=None):
        self.config = self.read_to_dict(input)
        self._logger, self._save_path = self.load_logger()
        self.update_config(log={'path': self._save_path})
        # initiate device environments
        os.environ["CUDA_VISIBLE_DEVICES"] = self.config['device']['gpu_ids']

    @property
    def logger(self):
        return self._logger

    @property
    def save_path(self):
        return self._save_path

    # read the config file
    def read_to_dict(self, input):
        if not input:
            return dict()
        if isinstance(input, str) and os.path.isfile(input):
            if input.endswith('yaml'):
                with open(input, 'r') as f:
                    config = yaml.load(f, Loader=yaml.FullLoader)
            else:
                ValueError('Config file should be with the format of *.yaml')
        elif isinstance(input, dict):
            config = input
        else:
            raise ValueError('Unrecognized input type (i.e. not *.yaml file nor dict).')

        return config

    # get the logger
    def load_logger(self):
        # set file handler
        save_path = os.path.join(self.config['log']['path'], datetime.now().isoformat())
        if not os.path.exists(save_path):
            os.makedirs(save_path)

        logfile = os.path.join(save_path, 'log.txt')
        file_handler = logging.FileHandler(logfile)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.__file_handler = file_handler

        # configure logger
        logger = logging.getLogger('Empty')
        logger.setLevel(logging.INFO)
        logger.addHandler(file_handler)

        return logger, save_path

    # update the config file, add new items or update existing items
    def update_config(self, *args, **kwargs):
        cfg1 = dict()
        for item in args:
            cfg1.update(self.read_to_dict(item))

        cfg2 = self.read_to_dict(kwargs)

        new_cfg = {**cfg1, **cfg2}

        update_recursive(self.config, new_cfg)
        # when update config file, the corresponding logger should also be updated.
        self.__update_logger()

    def __update_logger(self):
        # configure logger
        name = self.config['mode'] if 'mode' in self.config else self._logger.name
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        logger.addHandler(self.__file_handler)
        self._logger = logger

    def log_string(self, content):
        self._logger.info(content)
        print(content)

# 这是一个用于加载外部配置的函数，它会根据配置文件中的数据集名称，加载对应的数据集配置文件，并将其挂载到配置文件中。
def mount_external_config(cfg):
    if cfg.config['data']['dataset'] == 'scannet':
        from configs.scannet_config import ScannetConfig
        dataset_config = ScannetConfig()
        setattr(cfg, 'dataset_config', dataset_config)

        # Used for AP calculation
        eval_cfg = cfg.config.get('val', cfg.config.get('test'))
        CONFIG_DICT = {'remove_empty_box': not eval_cfg['faster_eval'],
                       'use_3d_nms': eval_cfg['use_3d_nms'],
                       'nms_iou': eval_cfg['nms_iou'],
                       'use_old_type_nms': eval_cfg['use_old_type_nms'],
                       'cls_nms': eval_cfg['use_cls_nms'],
                       'per_class_proposal': eval_cfg['per_class_proposal'],
                       'conf_thresh': eval_cfg['conf_thresh'],
                       'dataset_config': dataset_config}

        setattr(cfg, 'eval_config', CONFIG_DICT)
    return cfg