train:
  seed: 1999
  epochs: 600
  interval: 16
  pretrain: null  # MAFT预训练模型路径（可选）
  
test:
  seed: 1999
  test_topk_per_scene: 100
  test_score_thresh: 0.0
  test_npoint_thresh: 100

dataloader:
  train:
    batch_size: 2
    num_workers: 4
    persistent_workers: True
  val:
    batch_size: 1
    num_workers: 4
    persistent_workers: True
  test:
    batch_size: 1
    num_workers: 4
    persistent_workers: True

data:
  train:
    type: scannetv2
    data_root: datasets/scannet
    prefix: train
    training: True
    mode: 4
    with_elastic: True
    bbox_root: datasets/scannet/processed_data
    zs_root: datasets/bsp/zs
    voxel_cfg:
      scale: 50
      spatial_shape: [128, 512]
      max_npoint: 250000
  val:
    type: scannetv2
    data_root: datasets/scannet
    prefix: val
    training: False
    mode: 4
    with_elastic: False
    bbox_root: datasets/scannet/processed_data
    zs_root: datasets/bsp/zs
    voxel_cfg:
      scale: 50
      spatial_shape: [128, 512]
      max_npoint: 250000
  test:
    type: scannetv2
    data_root: datasets/scannet
    prefix: test
    training: False
    mode: 4
    with_elastic: False
    bbox_root: datasets/scannet/processed_data
    zs_root: datasets/bsp/zs
    voxel_cfg:
      scale: 50
      spatial_shape: [128, 512]
      max_npoint: 250000

model:
  name: MAFT
  # MAFT配置
  input_channel: 6
  blocks: 5
  block_reps: 2
  media: 32
  normalize_before: True
  return_blocks: True
  pool: mean
  num_class: 25
  
  # MAFT Decoder配置
  decoder:
    num_layer: 6
    num_query: 400
    d_model: 256
    nhead: 8
    hidden_dim: 1024
    dropout: 0.0
    activation_fn: gelu
    iter_pred: True
    attn_mask: True
    pe: False
    temperature: 10000
    pos_type: fourier
    attn_mask_thresh: 0.1
    quant_grid_length: 24
    grid_size: 0.1
    rel_query: True
    rel_key: True
    rel_value: True
  
  # MAFT损失配置
  criterion:
    sem_weight: 1.0
    geo_weight: 0.5
    loss_weight: [1.0, 1.0, 3.0, 1.0, 0.5, 1.0]  # [cls, mask_bce, mask_dice, bbox, score, z]
    
    cost_weight: [2.0, 5.0, 2.0]  # （class, mask_dice, bbox）
    non_object_weight: 0.1
    
    bbox_weights:
      center: 1.5
      size: 0.5
      angle: 0.5
      giou: 1.0
  
  
  test_cfg:
    topk_insts: 100
    score_thr: 0.0
    npoint_thr: 5
    
  norm_eval: False
  fix_module: []

optimizer:
  type: AdamW
  lr: 0.0001
  weight_decay: 0.05

lr_scheduler:
  type: PolyLR
  max_iters: 600
  power: 0.9
  constant_ending: 0.0

# 评估配置
evaluation:
  # 评估频率
  eval_interval: 8
  save_best: True
  
# 输出配置
output:
  exp_name: maft_scannet
  save_pred_instance: True 