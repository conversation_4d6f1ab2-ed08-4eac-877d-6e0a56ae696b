# ScanNetDataset接口文档

## 类概述
`ScanNetDataset`是一个统一的ScanNet数据集加载类，支持可选的边界框(bbox)和Z-score加载功能。该类继承自PyTorch的`Dataset`类，用于3D点云实例分割任务。

## 初始化参数

```python
def __init__(self,
             data_root: str,                        # 数据集根目录
             prefix: str,                           # 数据集前缀（如'train', 'val', 'test'）
             suffix: str,                           # 文件后缀
             voxel_cfg: Dict[str, Any] | None = None, # 体素化配置
             training: bool = True,                 # 是否为训练模式
             with_label: bool = True,               # 是否加载标签
             mode: int = 4,                         # 体素化模式
             with_elastic: bool = True,             # 是否使用弹性变换
             use_xyz: bool = True,                  # 是否使用坐标作为特征
             logger: Any | None = None,             # 日志记录器
             use_normalized: bool = False,          # 是否使用归一化坐标
             exclude_zero_gt: bool = False,         # 是否排除零标签
             with_normals: bool = False,            # 是否加载法向量
             num_classes: int = 20,                 # 类别数量
             stuff_class_ids: List[int] | Tuple[int, ...] = (0, 1), # 背景类ID
             sub_epoch_size: int = 3000,            # 子轮次大小
             include_bbox: bool = False,            # 是否包含边界框
             include_zs: bool = False,              # 是否包含Z-score
             bbox_root: str | None = None,          # 边界框数据根目录
             zs_root: str | None = None):           # Z-score数据根目录
```

## 常量

```python
CLASSES = (
    'cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window',
    'bookshelf', 'picture', 'counter', 'desk', 'curtain',
    'refrigerator', 'shower curtain', 'toilet', 'sink', 'bathtub',
    'otherfurniture')
NYU_ID = (3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 24, 28, 33, 34, 36, 39)
```

## 主要方法

### 1. `__len__(self) -> int`
返回数据集中样本的数量。

### 2. `__getitem__(self, index: int) -> Tuple`
获取指定索引的数据样本。

**返回值**:
```python
(
    scan_id,           # 扫描ID (字符串)
    coord,             # 体素坐标 (torch.Tensor)
    coord_float,       # 浮点坐标 (torch.Tensor)
    feat,              # 特征向量 (torch.Tensor)
    superpoint,        # 超点索引 (torch.Tensor)
    inst,              # 实例对象 (Instances3D)
    normal,            # 法向量 (torch.Tensor 或 None)
    bboxes,            # 边界框数据 (List 或 None)
    zs,                # Z-score数据 (numpy.ndarray 或 None)
    shapenet_catids,   # ShapeNet类别ID (List 或 None)
    shapenet_ids       # ShapeNet ID (List 或 None)
)
```

### 3. `collate_fn(self, batch: Sequence[Tuple]) -> Dict[str, Any]`
将批次数据整合为模型输入格式。

**返回值**:
```python
{
    'scan_ids': List[str],                # 扫描ID列表
    'voxel_coords': torch.Tensor,         # 体素坐标
    'p2v_map': torch.Tensor,              # 点到体素映射
    'v2p_map': torch.Tensor,              # 体素到点映射
    'spatial_shape': np.ndarray,          # 空间形状
    'feats': torch.Tensor,                # 特征向量
    'superpoints': torch.Tensor,          # 超点索引
    'batch_offsets': torch.Tensor,        # 批次偏移量
    'insts': List[Instances3D],           # 实例对象列表
    'coords_float': torch.Tensor,         # 浮点坐标
    'bboxes': List,                       # 边界框数据列表
    'zs': List,                           # Z-score数据列表
    'shapenet_catids': List,              # ShapeNet类别ID列表
    'shapenet_ids': List                  # ShapeNet ID列表
}
```

### 4. `get_instance3D(self, instance_label, semantic_label, superpoint, coord_float, scan_id, original_bboxes_for_scene)`
从标签和边界框数据构建Instances3D对象。

**参数**:
- `instance_label`: 实例标签 (torch.Tensor)
- `semantic_label`: 语义标签 (torch.Tensor)
- `superpoint`: 超点索引 (torch.Tensor)
- `coord_float`: 浮点坐标 (torch.Tensor)
- `scan_id`: 扫描ID (str)
- `original_bboxes_for_scene`: 原始边界框数据 (List[Dict] 或 None)

**返回值**:
- `inst`: Instances3D对象，包含以下属性:
  - `gt_instances`: 实例标签 (numpy.ndarray)
  - `gt_labels`: 类别标签 (torch.Tensor)
  - `gt_spmasks`: 超点掩码 (torch.Tensor)
  - `gt_bboxes`: 边界框 (torch.Tensor)
  - `gt_masks`: 实例掩码 (torch.Tensor)

## 辅助方法

### 1. `get_filenames(self) -> List[str]`
获取数据集文件列表。

### 2. `load(self, filename: str)`
加载指定文件的数据。

### 3. `transform_train(self, xyz, rgb, superpoint, semantic_label, instance_label, normal=None)`
对训练数据进行变换增强。

### 4. `transform_test(self, xyz, rgb, superpoint, semantic_label=None, instance_label=None, normal=None)`
对测试数据进行变换。

### 5. `data_aug(self, xyz, jitter=False, flip=False, rot=False, normal=None)`
数据增强函数，包括抖动、翻转和旋转。

### 6. `crop(self, xyz: np.ndarray) -> Tuple[np.ndarray, np.ndarray]`
裁剪点云数据。

### 7. `elastic(self, xyz, gran, mag)`
弹性变换函数。

### 8. `get_cropped_inst_label(self, instance_label, valid_idxs)`
获取裁剪后的实例标签。

## 使用示例

```python
# 初始化数据集
dataset = ScanNetDataset(
    data_root='/path/to/scannet',
    prefix='train',
    suffix='_inst_nostuff.pth',
    voxel_cfg={
        'scale': 50,
        'spatial_shape': [128, 512, 512],
        'max_npoint': 250000
    },
    include_bbox=True,
    bbox_root='/path/to/bboxes'
)

# 获取单个样本
scan_id, coord, coord_float, feat, superpoint, inst, normal, bboxes, zs, catids, ids = dataset[0]

# 使用DataLoader加载批次数据
from torch.utils.data import DataLoader
dataloader = DataLoader(
    dataset,
    batch_size=4,
    shuffle=True,
    num_workers=4,
    collate_fn=dataset.collate_fn
)

# 迭代批次数据
for batch in dataloader:
    # 访问批次数据
    scan_ids = batch['scan_ids']
    voxel_coords = batch['voxel_coords']
    feats = batch['feats']
    insts = batch['insts']
    # 处理数据...
```

## 注意事项

1. 使用前需确保正确设置导入路径，特别是`model.maft.utils`模块:
   ```python
   import sys
   sys.path.append(osp.dirname(osp.dirname(osp.abspath(__file__))))
   from model.maft.utils import Instances3D
   ```

2. 边界框数据格式应为包含`instance_id`和`box3D`字段的字典列表。

3. 当`include_bbox=True`时，需要提供有效的`bbox_root`路径。

4. 当`include_zs=True`时，需要提供有效的`zs_root`路径。

5. 数据增强仅在`training=True`时应用。

## 边界框数据格式

边界框数据应存储为pickle文件，每个场景一个文件，路径格式为`{bbox_root}/{scan_id}/bbox.pkl`。每个pickle文件包含一个字典列表，每个字典代表一个实例的边界框，格式如下:

```python
[
    {
        'instance_id': 1,  # 1-indexed实例ID
        'box3D': [x, y, z, dx, dy, dz, theta],  # 7维边界框参数
        'shapenet_catid': '03001627',  # (可选) ShapeNet类别ID
        'shapenet_id': '1a2b3c'  # (可选) ShapeNet实例ID
    },
    # 更多实例...
]
```

## Z-score数据格式

Z-score数据应存储为numpy压缩文件(.npz)，每个场景一个文件，路径格式为`{zs_root}/{scan_id}/zs.npz`。每个npz文件包含一个名为'zs'的数组。

## 导入路径问题解决

如果遇到`No module named 'maft'`错误，请在`scannetv2.py`文件开头添加以下代码:

```python
import sys
# 添加当前目录到系统路径
sys.path.append(osp.dirname(osp.dirname(osp.abspath(__file__))))
from model.maft.utils import Instances3D
```

这将确保Python能够找到`model.maft.utils`模块，从而成功导入`Instances3D`类。 