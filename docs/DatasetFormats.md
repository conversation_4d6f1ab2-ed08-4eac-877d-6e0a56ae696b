# TIMR数据集格式文档

本文档详细描述了TIMR项目中`datasets/`目录下的数据集内容和格式。

## 目录结构

```
TIMR/datasets/
├── scannet/                # ScanNet数据集
│   ├── splits/             # 数据集划分文件
│   │   ├── train.txt       # 训练集场景ID列表
│   │   ├── val.txt         # 验证集场景ID列表
│   │   └── test.txt        # 测试集场景ID列表
│   ├── scans/              # 原始扫描数据
│   │   ├── scene****_**_inst_nostuff.pth    # 实例分割数据
│   │   ├── scene****_**_normals.pth         # 法向量数据
│   │   └── scene****_**_vh_clean_2.ply      # 点云几何数据
│   ├── processed_data/     # 处理后的数据
│   │   └── scene****_**/   # 每个场景的处理后数据
│   │       ├── data.npz    # 点云、语义标签和实例标签
│   │       └── bbox.pkl    # 边界框数据
│   └── rfs_label_map.csv   # 标签映射文件
├── ShapeNetv2_data/        # ShapeNet数据
│   ├── pointcloud.tar.gz   # 点云数据压缩包
│   ├── point.tar.gz        # 点数据压缩包
│   ├── voxel.tar.gz        # 体素数据压缩包
│   └── watertight_scaled_simplified/  # 简化的水密模型
│       ├── 02747177/       # 垃圾桶类别
│       ├── 04379243/       # 桌子类别
│       ├── 04256520/       # 沙发类别
│       ├── 02808440/       # 浴缸类别
│       ├── 03211117/       # 显示器类别
│       ├── 02871439/       # 书架类别
│       ├── 03001627/       # 椅子类别
│       └── 02933112/       # 柜子类别
├── splits/                 # 其他数据集划分
└── bsp/                    # BSP相关数据
    ├── model.pth.tar       # BSP模型权重文件
    ├── database_scannet.npz # BSP数据库文件
    └── zs/                 # 每个场景的Z-score特征
        └── scene****_**/   # 场景目录
            └── zs.npz      # Z-score特征文件
```

## ScanNet数据集

### 1. 数据集划分 (`splits/`)

包含三个文本文件，每行一个场景ID：
- `train.txt`: 1181个训练场景
- `val.txt`: 311个验证场景
- `test.txt`: 311个测试场景

### 2. 原始扫描数据 (`scans/`)

每个场景包含三个文件：
- `scene****_**_inst_nostuff.pth`: PyTorch格式的实例分割数据
- `scene****_**_normals.pth`: PyTorch格式的法向量数据
- `scene****_**_vh_clean_2.ply`: PLY格式的点云几何数据

### 3. 处理后的数据 (`processed_data/`)

每个场景有一个子目录，包含两个文件：

#### 3.1 `data.npz`

包含以下NumPy数组：
- `mesh_vertices`: 形状为 (N, 6) 的数组，其中N是点的数量，6个通道包括XYZ坐标和RGB颜色
- `semantic_labels`: 形状为 (N,) 的数组，表示每个点的语义标签
- `instance_labels`: 形状为 (N,) 的数组，表示每个点的实例标签

#### 3.2 `bbox.pkl`

包含场景中所有实例的边界框信息，格式为字典列表，每个字典包含：
- `box3D`: 7维数组 [x, y, z, dx, dy, dz, theta]，表示3D边界框的中心坐标、尺寸和旋转角度
- `cls_id`: 类别ID
- `shapenet_catid`: ShapeNet类别ID，如'04379243'（桌子）
- `shapenet_id`: ShapeNet实例ID
- `instance_id`: 实例ID
- `box_corners`: 8x3数组，表示边界框的8个角点坐标
- `sym`: 对称类型，如'__SYM_NONE'表示无对称性

### 4. 标签映射 (`rfs_label_map.csv`)

CSV格式的标签映射文件，包含以下列：
- `id`: 原始ID
- `raw_category`: 原始类别名称
- `ShapeNetCore55`: ShapeNet类别名称
- `synsetoffset`: ShapeNet类别ID
- `nyu40class`: NYU40类别名称
- `nyu40id`: NYU40类别ID
- `nyu_ids`: NYU ID映射
- `cad_ids`: CAD ID映射
- `rfs_labels`: RFS标签名称
- `rfs_ids`: RFS标签ID

类别映射关系：
- NYU类别(20类): 'wall', 'floor', 'cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window', 'bookshelf', 'picture', 'counter', 'desk', 'curtain', 'refridgerator', 'shower curtain', 'toilet', 'sink', 'bathtub', 'otherfurniture'
- CAD类别(8类): 'table', 'chair', 'bookshelf', 'sofa', 'trash_bin', 'cabinet', 'display', 'bathtub'

## ShapeNet数据集

### 1. 压缩包

- `pointcloud.tar.gz`: 点云数据
- `point.tar.gz`: 点数据
- `voxel.tar.gz`: 体素数据

### 2. 水密模型 (`watertight_scaled_simplified/`)

包含8个类别的3D模型，每个类别对应一个目录：
- `02747177/`: 垃圾桶
- `04379243/`: 桌子
- `04256520/`: 沙发
- `02808440/`: 浴缸
- `03211117/`: 显示器
- `02871439/`: 书架
- `03001627/`: 椅子
- `02933112/`: 柜子

每个目录包含该类别的多个实例，每个实例是一个OFF格式的3D模型文件。

## BSP数据

BSP (Binary Space Partitioning) 相关数据用于形状表示和重建。

### 1. 模型权重 (`model.pth.tar`)

预训练的BSP模型权重文件，包含以下组件：
- `encoder`: 编码器网络的权重（卷积层、批归一化层等）
- `decoder`: 解码器网络的权重（线性层、批归一化层等）
- `generator`: 生成器网络的权重，包含凸层和凹层权重

### 2. 数据库文件 (`database_scannet.npz`)

包含ScanNet数据集中物体的Z-score特征数据库，包含以下字段：
- `zs`: 形状为 (2238, 256) 的数组，表示2238个物体的256维Z-score特征向量
- `lbls`: 形状为 (2238,) 的数组，表示每个物体的类别标签
- `names`: 形状为 (2238,) 的数组，表示每个物体的文件名

### 3. Z-score特征 (`zs/`)

包含每个场景的Z-score特征，每个场景一个目录，目录下包含一个`zs.npz`文件：
- `zs.npz`: 包含一个名为`zs`的数组，形状为 (M, 256)，其中M是场景中物体的数量，256是特征维度

Z-score特征是物体的隐空间表示，用于形状匹配和重建。在TIMR模型中，这些特征用于连接3D检测和形状重建任务。

## 数据处理流程

项目中的`generate_data_relabel.py`脚本用于处理原始ScanNet数据，生成处理后的数据。主要步骤包括：

1. 读取ScanNet原始数据（PLY文件、聚合文件、分割文件）
2. 从Scan2CAD注释中提取对应的ShapeNet模型信息
3. 将ShapeNet模型变换到ScanNet坐标系中
4. 生成7D边界框（中心、尺寸、旋转角度）
5. 重新标记实例标签，处理特殊类别（如柜子、显示器、垃圾桶等）
6. 保存处理后的数据到`data.npz`和`bbox.pkl`文件

## 数据加载

数据通过`ScanNetDataset`类加载，支持以下功能：
- 加载点云、语义标签、实例标签
- 加载法向量（可选）
- 加载边界框（可选）
- 加载Z-score（可选）
- 数据增强（训练时）：旋转、翻转、抖动、弹性变换
- 点云裁剪

详细的API文档请参考[ScanNetDataset_API.md](./ScanNetDataset_API.md)。 