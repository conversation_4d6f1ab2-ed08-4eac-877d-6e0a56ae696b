# TIMR: Transformer-based Instance Mesh Reconstruction

## 项目概述

TIMR (Transformer-based Instance Mesh Reconstruction) 是一个端到端的3D实例分割和网格重建项目，融合了QIMR的MAFT模型和DIMR的重建框架。

### 核心特性

- **端到端处理**: 从原始点云直接到完整实例网格
- **MAFT分割**: 基于Transformer的多尺度注意力融合实例分割
- **BSP重建**: 基于Binary Space Partitioning的高质量网格重建
- **统一框架**: 整合分割和重建的联合优化

## 技术架构

### 分割模块 (基于QIMR-MAFT)
- Multi-scale Attention Fusion Transformer
- Query-based实例检测
- 形状潜码预测

### 重建模块 (基于DIMR-BSP)  
- Binary Space Partitioning网络
- 几何约束优化
- 网格生成与优化

### 数据流程
```
输入点云 → MAFT分割 → 实例特征 → BSP重建 → 输出网格
```

## 环境配置

```bash
# 基础依赖
pip install torch torchvision
pip install spconv-cu118  # 根据CUDA版本选择
pip install trimesh open3d

# 编译自定义算子
cd lib/pointgroup_ops && python setup.py develop
cd lib/bspt && python setup.py develop
```

## 快速开始

```bash
# 训练
python train.py --config configs/timr_scannet.yaml

# 测试  
python test.py --config configs/timr_scannet.yaml --checkpoint checkpoints/timr.pth
```

## 数据格式

支持ScanNet数据集，数据预处理请参考：
- 点云分割标注
- 实例网格标注
- BSP潜码生成

## 模型性能

待测试和评估...

## 致谢

本项目基于以下优秀工作：
- QIMR: 提供MAFT实例分割模型
- DIMR: 提供BSP重建框架 