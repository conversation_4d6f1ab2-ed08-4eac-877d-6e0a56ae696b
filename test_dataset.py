#!/usr/bin/env python3
import argparse
import os
import sys
import yaml
import torch
import logging

# Add project path, assuming test_dataset.py is in the TIMR directory
# and 'data', 'utils' are accessible from there.
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
# 添加 QIMR 路径，因为 model.maft.dataset 模块在这个路径下
sys.path.append('/home/<USER>/code/QIMR')
# 添加 maft 模块路径
sys.path.append('/home/<USER>/Files/QIMR')

try:
    from data import build_dataset, build_dataloader
    from utils.logger import get_logger # Assuming get_logger can handle console output or is adapted
except ImportError as e:
    print(f"Error importing project modules: {e}")
    print("Please ensure that 'data' and 'utils' modules are correctly placed and accessible.")
    print(f"Current sys.path includes: {sys.path}")
    sys.exit(1)

def setup_logger():
    """Sets up a simple logger to print to console."""
    logger = logging.getLogger('DatasetTest')
    logger.setLevel(logging.INFO)
    if not logger.handlers: # Avoid adding multiple handlers if re-running in same session
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        ch.setFormatter(formatter)
        logger.addHandler(ch)
    return logger

def test_dataset_loading(config_path, num_batches_to_test=3):
    """
    Tests dataset and dataloader functionality.
    """
    logger = setup_logger()
    
    logger.info(f"Loading configuration from: {config_path}")
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        logger.error(f"Configuration file not found: {config_path}")
        return
    except Exception as e:
        logger.error(f"Error loading or parsing config file: {e}")
        return

    # Ensure necessary config sections exist
    if 'data' not in config or 'train' not in config['data']:
        logger.error("Config missing 'data' or 'data.train' section.")
        return
    if 'dataloader' not in config or 'train' not in config['dataloader']:
        logger.error("Config missing 'dataloader' or 'dataloader.train' section.")
        return

    # Build train dataset
    logger.info("Attempting to build train dataset...")
    try:
        train_dataset_config = config['data']['train']
        # Note: build_dataset in train.py takes (config, logger)
        # Adapting based on typical usage. If get_logger from utils.logger is specific,
        # it might need to be used directly if it's essential for build_dataset.
        # For this demo, passing our general logger.
        train_dataset = build_dataset(train_dataset_config, logger)
        logger.info(f"Successfully built train dataset. Number of samples: {len(train_dataset)}")
    except Exception as e:
        logger.error(f"Error building train dataset: {e}", exc_info=True)
        return

    # Build train dataloader
    logger.info("Attempting to build train dataloader...")
    try:
        train_dataloader_config = config['dataloader']['train']
        train_loader = build_dataloader(
            train_dataset,
            training=True, # As typically used for training loader
            **train_dataloader_config
        )
        logger.info("Successfully built train dataloader.")
    except Exception as e:
        logger.error(f"Error building train dataloader: {e}", exc_info=True)
        return

    # Iterate over a few batches and print info
    logger.info(f"Fetching and inspecting {num_batches_to_test} batch(es) from the train dataloader...")
    try:
        for i, batch in enumerate(train_loader):
            if i >= num_batches_to_test:
                break
            logger.info(f"--- Batch {i+1} / {num_batches_to_test} ---")
            
            if isinstance(batch, dict):
                logger.info(f"Batch is a dictionary with keys: {list(batch.keys())}")
                for key, value in batch.items():
                    if isinstance(value, torch.Tensor):
                        logger.info(f"  Key: '{key}', Type: torch.Tensor, Shape: {value.shape}, Dtype: {value.dtype}, Device: {value.device}")
                    elif isinstance(value, list):
                        first_elem_type = type(value[0]) if value else 'N/A'
                        logger.info(f"  Key: '{key}', Type: List, Count: {len(value)}, First_elem_type: {first_elem_type}")
                        if value and isinstance(value[0], torch.Tensor):
                            logger.info(f"    (List of Tensors) First_Tensor_Shape: {value[0].shape}, First_Tensor_Dtype: {value[0].dtype}")
                    elif isinstance(value, tuple):
                        first_elem_type = type(value[0]) if value else 'N/A'
                        logger.info(f"  Key: '{key}', Type: Tuple, Count: {len(value)}, First_elem_type: {first_elem_type}")
                    else:
                        logger.info(f"  Key: '{key}', Type: {type(value)}, Value_preview: {str(value)[:100]}") # Preview non-tensor, non-list/tuple data
            elif isinstance(batch, (list, tuple)):
                logger.info(f"Batch is a {type(batch).__name__} of length {len(batch)}.")
                if batch:
                    first_elem = batch[0]
                    if isinstance(first_elem, torch.Tensor):
                        logger.info(f"  First element: Type: torch.Tensor, Shape: {first_elem.shape}, Dtype: {first_elem.dtype}, Device: {first_elem.device}")
                    else:
                        logger.info(f"  First element: Type: {type(first_elem)}")
            elif isinstance(batch, torch.Tensor):
                 logger.info(f"Batch is a torch.Tensor. Shape: {batch.shape}, Dtype: {batch.dtype}, Device: {batch.device}")
            else:
                logger.info(f"Batch type: {type(batch)}, Value_preview: {str(batch)[:200]}")

            if i == 0 and hasattr(train_dataset, '__getitem__'):
                try:
                    logger.info("--- Inspecting first item from dataset (__getitem__[0]) ---")
                    first_item = train_dataset[0]
                    if isinstance(first_item, dict):
                        logger.info(f"First item is a dictionary with keys: {list(first_item.keys())}")
                        for key, value in first_item.items():
                            if isinstance(value, torch.Tensor):
                                logger.info(f"  Key: '{key}', Type: torch.Tensor, Shape: {value.shape}, Dtype: {value.dtype}")
                            else:
                                logger.info(f"  Key: '{key}', Type: {type(value)}, Value_preview: {str(value)[:100]}")
                    else:
                         logger.info(f"First item from dataset: Type: {type(first_item)}, Preview: {str(first_item)[:200]}")
                except Exception as e:
                    logger.warning(f"Could not retrieve or inspect first item from dataset: {e}", exc_info=True)
        
        if i < num_batches_to_test -1 and i < len(train_loader) -1 :
             logger.warning(f"Could only fetch {i+1} batches. Dataloader might be smaller than {num_batches_to_test} or an error occurred.")
        elif len(train_loader) == 0:
            logger.warning("The dataloader is empty.")


    except Exception as e:
        logger.error(f"Error during batch iteration or inspection: {e}", exc_info=True)

    logger.info("Dataset loading test finished.")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Test TIMR Dataset Loading')
    parser.add_argument(
        '--config', 
        type=str, 
        required=True, 
        help='Path to the configuration file (e.g., configs/your_config.yaml)'
    )
    parser.add_argument(
        '--num_batches',
        type=int,
        default=3,
        help='Number of batches to fetch and inspect from the dataloader.'
    )
    args = parser.parse_args()
    
    test_dataset_loading(args.config, args.num_batches) 