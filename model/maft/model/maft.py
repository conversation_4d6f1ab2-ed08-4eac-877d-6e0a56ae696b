import functools
import gorilla
import numpy as np
import pointgroup_ops
import spconv.pytorch as spconv
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_scatter import scatter_max, scatter_mean

from ..utils import cuda_cast, rle_encode
from .backbone import ResidualBlock, UBlock
from .loss import Criterion
from .query_decoder import QueryDecoder

class MAFT(nn.Module):
    def __init__(
        self,
        input_channel: int = 6,
        blocks: int = 5,
        block_reps: int = 2,
        media: int = 32,
        normalize_before=True,
        return_blocks=True,
        pool='mean',
        num_class=18,
        decoder=None,
        criterion=None,
        test_cfg=None,
        norm_eval=False,
        fix_module=[],
    ):
        super(MAFT, self).__init__()
        self.input_conv = spconv.SparseSequential(
            spconv.SubMConv3d(
                input_channel,
                media,
                kernel_size=3,
                padding=1,
                bias=False,
                indice_key='subm1',
            ))
        block = ResidualBlock
        norm_fn = functools.partial(nn.BatchNorm1d, eps=1e-4, momentum=0.1)
        block_list = [media * (i + 1) for i in range(blocks)]
        # UNet，用于提取特征
        self.unet = UBlock(
            block_list,
            norm_fn,
            block_reps,
            block,
            indice_key_id=1,
            normalize_before=normalize_before,
            return_blocks=return_blocks,
        )
        # 输出层
        self.output_layer = spconv.SparseSequential(norm_fn(media), nn.ReLU(inplace=True))
        # 池化方式
        self.pool = pool
        # 类别数
        self.num_class = num_class
        
        # decoder
        self.decoder = QueryDecoder(**decoder, in_channel=media, num_class=num_class)

        # shape latent head
        self.latent_dim = 256
        # 例如主干输出的实例特征维度为 feat_dim，这里假设512
        self.latent_head = nn.Sequential(
            nn.Linear(media, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, self.latent_dim)
        )
        
        # 损失函数
        self.criterion = Criterion(**criterion, num_class=num_class, latent_dim=self.latent_dim)
        
        self.test_cfg = test_cfg
        self.norm_eval = norm_eval  # 是否固定BN层
        # 固定某些模块
        for module in fix_module:
            module = getattr(self, module)
            module.eval()
            for param in module.parameters():
                param.requires_grad = False
    
    # 训练模式
    def train(self, mode=True):
        super(MAFT, self).train(mode)
        if mode and self.norm_eval:
            for m in self.modules():
                # trick: eval have effect on BatchNorm1d only
                if isinstance(m, nn.BatchNorm1d):
                    m.eval()
    
    def forward(self, batch, mode='loss'):
        if mode == 'loss':
            return self.loss(batch)
        elif mode == 'predict':
            return self.predict(batch)
        
    # cuda_cast 装饰器，用于将输入和输出转换为cuda张量
    @cuda_cast
    def loss(self, batch):
        # 获取输入数据
        voxel_coords  = batch['voxel_coords']
        p2v_map       = batch['p2v_map']
        v2p_map       = batch['v2p_map']
        spatial_shape = batch['spatial_shape']
        feats         = batch['feats']
        insts         = batch['insts']
        superpoints   = batch['superpoints']
        coords_float  = batch['coords_float']
        batch_offsets = batch['batch_offsets']
        bboxes        = batch.get('bboxes', None)
        zs            = batch.get('zs', None)
        
        batch_size = len(batch_offsets) - 1
        # 将点云数据转换为体素数据
        voxel_feats = pointgroup_ops.voxelization(feats, v2p_map)
        # 将体素数据转换为稀疏卷积张量
        input = spconv.SparseConvTensor(voxel_feats, voxel_coords.int(), spatial_shape, batch_size)
        # 提取特征
        sp_feats = self.extract_feat(input, superpoints, p2v_map)
        
        sp_coords_float = scatter_mean(coords_float, superpoints, dim=0)  # (B*M, media)
        out = self.decoder(sp_feats, sp_coords_float, batch_offsets)
        
        loss, loss_dict = self.criterion(out, insts, gt_zs=zs, pred_zs=out['zs'])
        # 添加z_loss
        loss_dict['z_loss'] = loss_dict.get('z_loss', torch.tensor(0.))
        
        return loss, loss_dict
    
    @cuda_cast
    def predict(self, batch):
        scan_ids = batch['scan_ids']
        voxel_coords  = batch['voxel_coords']
        p2v_map       = batch['p2v_map']
        v2p_map       = batch['v2p_map']
        spatial_shape = batch['spatial_shape']
        feats         = batch['feats']
        insts         = batch['insts']
        superpoints   = batch['superpoints']
        coords_float  = batch['coords_float']
        batch_offsets = batch['batch_offsets']
        bboxes        = batch.get('bboxes', None)
        
        batch_size = len(batch_offsets) - 1
        voxel_feats = pointgroup_ops.voxelization(feats, v2p_map)
        input = spconv.SparseConvTensor(voxel_feats, voxel_coords.int(), spatial_shape, batch_size)

        sp_feats = self.extract_feat(input, superpoints, p2v_map)

        sp_coords_float = scatter_mean(coords_float, superpoints, dim=0)  # (B*M, media)
        out = self.decoder(sp_feats, sp_coords_float, batch_offsets)

        ret = self.predict_by_feat(scan_ids, out, superpoints, insts)
        return ret
    
    def predict_by_feat(self, scan_ids, out, superpoints, insts):
        pred_labels = out['labels']
        pred_masks = out['masks']
        pred_scores = out['scores']
        pred_zs = out['zs']
        # 添加对bboxes的获取
        pred_bboxes = out.get('bboxes', None)
        
        
        scores = F.softmax(pred_labels[0], dim=-1)[:, :-1]
        scores *= pred_scores[0]
        labels = torch.arange(
            self.num_class, device=scores.device).unsqueeze(0).repeat(self.decoder.num_query, 1).flatten(0, 1)
        scores, topk_idx = scores.flatten(0, 1).topk(self.test_cfg['topk_insts'], sorted=False)
        labels = labels[topk_idx]
        labels += 1

        
        topk_idx = torch.div(topk_idx, self.num_class, rounding_mode='floor')
        mask_pred = pred_masks[0]
        mask_pred = mask_pred[topk_idx]
        
        # 同样对pred_zs进行Top-K选择
        zs_pred = pred_zs[0][topk_idx]  # [topk, latent_dim]
        
        # 如果有bboxes，也进行Top-K选择
        bboxes_pred = None
        if pred_bboxes is not None:
            # 直接使用30维格式，避免不必要的转换
            if pred_bboxes[0].shape[-1] == 30:  # 30维格式
                bboxes_pred = pred_bboxes[0][topk_idx]  # [topk, 30]
            else:
                # 7维格式保持不变
                assert pred_bboxes[0].shape[-1] == 7, f"期望7维bbox，但得到{pred_bboxes[0].shape[-1]}维"
                bboxes_pred = pred_bboxes[0][topk_idx]  # [topk, 7]
        
        mask_pred_sigmoid = mask_pred.sigmoid()
        # mask_pred before sigmoid()
        mask_pred = (mask_pred > 0).float()  # [n_p, M]
        mask_scores = (mask_pred_sigmoid * mask_pred).sum(1) / (mask_pred.sum(1) + 1e-6)
        scores = scores * mask_scores
        # get mask
        mask_pred = mask_pred[:, superpoints].int()

        # score_thr - 对所有预测结果同时过滤
        score_mask = scores > self.test_cfg['score_thr']
        scores = scores[score_mask]  # (n_p,)
        labels = labels[score_mask]  # (n_p,)
        mask_pred = mask_pred[score_mask]  # (n_p, N)
        zs_pred = zs_pred[score_mask]  # (n_p, latent_dim)
        if bboxes_pred is not None:
            bboxes_pred = bboxes_pred[score_mask]  # (n_p, 30) or (n_p, 7)

        
        # npoint thr - 对所有预测结果同时过滤
        mask_pointnum = mask_pred.sum(1)
        
            
        npoint_mask = mask_pointnum > self.test_cfg['npoint_thr']
        scores = scores[npoint_mask]  # (n_p,)
        labels = labels[npoint_mask]  # (n_p,)
        mask_pred = mask_pred[npoint_mask]  # (n_p, N)
        zs_pred = zs_pred[npoint_mask]  # (n_p, latent_dim)
        if bboxes_pred is not None:
            bboxes_pred = bboxes_pred[npoint_mask]  # (n_p, 30) or (n_p, 7)

        
        cls_pred = labels.cpu().numpy()
        score_pred = scores.cpu().numpy()
        mask_pred = mask_pred.cpu().numpy()
        zs_pred = zs_pred.cpu().numpy()  # 转换为numpy数组
        if bboxes_pred is not None:
            bboxes_pred = bboxes_pred.cpu().numpy()  # 转换为numpy数组

        pred_instances = []
        for i in range(cls_pred.shape[0]):
            pred = {}
            pred['scan_id'] = scan_ids[0]
            pred['label_id'] = cls_pred[i]
            pred['conf'] = score_pred[i]
            # rle encode mask to save memory
            pred['pred_mask'] = rle_encode(mask_pred[i])
            # 添加潜在编码
            pred['pred_zs'] = zs_pred[i]  # shape: [latent_dim]
            # 添加bboxes
            if bboxes_pred is not None:
                pred['bboxes'] = bboxes_pred[i]  # shape: [30] or [7] - 30维: [cx,cy,cz,w,h,d,cls12,reg12] 或 7维: [cx,cy,cz,w,h,d,angle]
            pred_instances.append(pred)

        gt_instances = insts[0].gt_instances
        return dict(scan_id=scan_ids[0], pred_instances=pred_instances, gt_instances=gt_instances)
    
    def extract_feat(self, input, superpoints, v2p_map):
        # backbone
        input = self.input_conv(input)
        input, _ = self.unet(input)
        input = self.output_layer(input)
        input = input.features[v2p_map.long()]  # (B*N, media)

        # superpoint pooling
        if self.pool == 'mean':
            input = scatter_mean(input, superpoints, dim=0)  # (B*M, media)
        elif self.pool == 'max':
            input, _ = scatter_max(input, superpoints, dim=0)  # (B*M, media)
        return input