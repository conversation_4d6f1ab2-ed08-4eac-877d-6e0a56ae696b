from maft.dataset.scannetv2 import ScanNetDataset
from torch.utils.data import DataLoader


def get_dataloader(cfg, mode):
    # 直接使用ScanNetDataset
    try:
        dataset = ScanNetDataset(
            data_root=cfg.config['data']['data_path'],
            prefix=mode,
            suffix='.pth',
            training=(mode == 'train'),
            with_label=(mode != 'test'),
            logger=cfg
        )
        
        dataloader = DataLoader(
            dataset=dataset,
            num_workers=cfg.config['device']['num_workers'],
            batch_size=cfg.config[mode]['batch_size'],
            shuffle=(mode == 'train'),
            collate_fn=dataset.collate_fn
        )
        return dataloader
    except Exception as e:
        cfg.log_string(f"Error creating dataloader: {e}")
        raise e 