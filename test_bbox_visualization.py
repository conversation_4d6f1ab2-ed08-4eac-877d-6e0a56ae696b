#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试bbox和实例对应关系的可视化脚本
按照scannetv2_inst.py的数据处理方式加载数据并可视化
"""

import os
import sys
import numpy as np
import torch
import open3d as o3d
import random
from typing import List, Tuple

# 添加项目路径
sys.path.append('/home/<USER>/code/TIMR')

from data.scannetv2_inst import ScanNetDataset
from configs.config_util import CONFIG
from utils.rfs_labels import RFS_CLASSES

def create_bbox_lineset(center: np.ndarray, size: np.ndarray, color: List[float], angle: float = 0) -> o3d.geometry.LineSet:
    """
    创建bbox的线框几何体（支持旋转角度）
    
    Args:
        center: bbox中心坐标 [x, y, z]
        size: bbox尺寸 [w, h, d]
        color: 颜色 [r, g, b]
        angle: 绕Z轴的旋转角度（弧度）
    
    Returns:
        o3d.geometry.LineSet: bbox线框
    """
    # 计算8个顶点（相对于中心的坐标）
    half_size = size / 2
    vertices = np.array([
        [-half_size[0], -half_size[1], -half_size[2]],  # 0
        [+half_size[0], -half_size[1], -half_size[2]],  # 1
        [+half_size[0], +half_size[1], -half_size[2]],  # 2
        [-half_size[0], +half_size[1], -half_size[2]],  # 3
        [-half_size[0], -half_size[1], +half_size[2]],  # 4
        [+half_size[0], -half_size[1], +half_size[2]],  # 5
        [+half_size[0], +half_size[1], +half_size[2]],  # 6
        [-half_size[0], +half_size[1], +half_size[2]],  # 7
    ])
    
    # 应用旋转变换（绕Z轴旋转）
    if angle != 0:
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation_matrix = np.array([
            [cos_a, -sin_a, 0],
            [sin_a,  cos_a, 0],
            [0,      0,     1]
        ])
        vertices = vertices @ rotation_matrix.T
    
    # 平移到实际中心位置
    vertices += center
    
    # 定义12条边
    lines = np.array([
        [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
        [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
        [0, 4], [1, 5], [2, 6], [3, 7],  # 竖直边
    ])
    
    # 创建LineSet
    lineset = o3d.geometry.LineSet()
    lineset.points = o3d.utility.Vector3dVector(vertices)
    lineset.lines = o3d.utility.Vector2iVector(lines)
    lineset.colors = o3d.utility.Vector3dVector([color] * len(lines))
    
    return lineset

def visualize_bbox_instance_match(dataset, sample_idx=0, max_instances=5):
    """
    使用Open3D可视化bbox和实例的匹配情况
    
    Args:
        dataset: ScanNetDataset实例
        sample_idx: 样本索引
        max_instances: 最大显示实例数量
    """
    print(f"正在加载样本 {sample_idx}...")
    
    # 获取数据
    try:
        data = dataset[sample_idx]
        scan_id, coord, coord_float, feat, superpoint, inst, normal, bboxes, zs = data
        print(f"数据加载成功")
    except Exception as e:
        print(f"数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return []
    
    print(f"场景ID: {scan_id}")
    print(f"点云数量: {len(coord_float)}")
    print(f"实例数量: {len(inst)}")
    print(f"有效bbox数量: {inst.gt_bbox_valid.sum().item()}")
    
    # 颜色列表 (RGB格式)
    colors = [
        [1.0, 0.0, 0.0],  # 红色
        [0.0, 0.0, 1.0],  # 蓝色
        [0.0, 1.0, 0.0],  # 绿色
        [1.0, 0.5, 0.0],  # 橙色
        [0.5, 0.0, 1.0],  # 紫色
        [0.6, 0.3, 0.0],  # 棕色
        [1.0, 0.0, 1.0],  # 粉色
        [0.0, 1.0, 1.0],  # 青色
    ]
    
    # 定义背景类别（需要过滤掉的类别）
    BACKGROUND_CLASSES = {'wall', 'floor', 'door', 'window', 'curtain', 'picture'}
    
    # 获取所有实例，但过滤掉背景类别
    all_instances = []
    valid_bbox_instances = []
    
    for i in range(min(len(inst), max_instances)):
        # 获取实例的类别标签
        label_id = inst.gt_labels[i].item()
        if label_id < len(RFS_CLASSES):
            class_name = RFS_CLASSES[label_id]
            # 只保留非背景类别的实例
            if class_name not in BACKGROUND_CLASSES:
                all_instances.append(i)
                if inst.gt_bbox_valid[i]:
                    valid_bbox_instances.append(i)
        else:
            # 未知类别也保留
            all_instances.append(i)
            if inst.gt_bbox_valid[i]:
                valid_bbox_instances.append(i)
    
    print(f"\n过滤后显示{len(all_instances)}个物体实例（其中{len(valid_bbox_instances)}个有bbox标注）:")
    print(f"已过滤的背景类别: {', '.join(BACKGROUND_CLASSES)}")
    
    # 创建可视化对象列表
    geometries = []
    
    # 创建实例掩码，用于从背景点云中排除实例点
    all_instance_mask = torch.zeros(len(coord_float), dtype=torch.bool)
    for i in all_instances:
        inst_mask = inst.gt_masks[i].bool()
        all_instance_mask |= inst_mask
    
    # 添加背景点云（灰色），排除实例点
    points = coord_float.numpy()
    background_mask = ~all_instance_mask.numpy()
    if background_mask.sum() > 0:
        background_points = points[background_mask]
        background_pcd = o3d.geometry.PointCloud()
        background_pcd.points = o3d.utility.Vector3dVector(background_points)
        background_pcd.colors = o3d.utility.Vector3dVector(np.full((len(background_points), 3), [0.7, 0.7, 0.7]))
        geometries.append(background_pcd)
    
    # 处理每个实例
    for idx, i in enumerate(all_instances):
        color = colors[idx % len(colors)]
        print(f"\n处理实例 {i}...")
        
        # 获取实例点云
        inst_mask = inst.gt_masks[i].bool()
        inst_points = coord_float[inst_mask].numpy()
        print(f"实例 {i} 点云数量: {len(inst_points)}")
        
        if len(inst_points) > 0:
            # 计算实例范围
            inst_min = inst_points.min(axis=0)
            inst_max = inst_points.max(axis=0)
            inst_center = (inst_min + inst_max) / 2
            inst_size = inst_max - inst_min
            
            print(f"实例 {i} (标签: {inst.gt_labels[i].item()})")
            print(f"  实例中心: [{inst_center[0]:.2f}, {inst_center[1]:.2f}, {inst_center[2]:.2f}]")
            print(f"  实例尺寸: [{inst_size[0]:.2f}, {inst_size[1]:.2f}, {inst_size[2]:.2f}]")
            
            # 检查是否有bbox标注和旋转角度
            has_rotation = False
            if inst.gt_bbox_valid[i]:
                # 获取bbox信息
                bbox = inst.gt_bboxes[i].numpy()
                bbox_center = bbox[:3]
                bbox_size = bbox[3:6]
                bbox_angle = bbox[6] if len(bbox) > 6 else 0
                
                print(f"  ✅ 有bbox标注")
                print(f"  bbox中心: [{bbox_center[0]:.2f}, {bbox_center[1]:.2f}, {bbox_center[2]:.2f}]")
                print(f"  bbox尺寸: [{bbox_size[0]:.2f}, {bbox_size[1]:.2f}, {bbox_size[2]:.2f}]")
                print(f"  bbox角度: {bbox_angle:.3f} 弧度 ({np.degrees(bbox_angle):.1f}°)")
                
                # 检查是否有旋转角度
                if abs(bbox_angle) > 0.01:  # 角度阈值，避免浮点数精度问题
                    has_rotation = True
                    print(f"  🔄 有旋转角度，使用鲜艳颜色")
                else:
                    print(f"  ➡️  角度为0，使用暗淡颜色")
            else:
                print(f"  ❌ 无bbox标注，使用暗淡颜色")
            
            # 根据是否有旋转角度决定颜色
            if has_rotation:
                # 有旋转角度：使用鲜艳颜色
                point_color = color
                print(f"  🎨 使用鲜艳颜色: {color}")
            else:
                # 无旋转角度或无bbox标注：使用暗淡颜色
                point_color = [c * 0.3 for c in color]  # 更暗的颜色
                print(f"  🎨 使用暗淡颜色: {point_color}")
            
            # 创建实例点云
            inst_pcd = o3d.geometry.PointCloud()
            inst_pcd.points = o3d.utility.Vector3dVector(inst_points)
            inst_pcd.colors = o3d.utility.Vector3dVector(np.full((len(inst_points), 3), point_color))
            geometries.append(inst_pcd)
            
            # 只有有旋转角度的实例才显示GT bbox
            if has_rotation and inst.gt_bbox_valid[i]:
                # 只创建GT bbox（亮色）
                gt_bbox = create_bbox_lineset(bbox_center, bbox_size, color, bbox_angle)
                geometries.append(gt_bbox)
                
                print(f"  📦 显示GT bbox框框")
        else:
            print(f"实例 {i} 没有点云数据，跳过")
    
    # 添加坐标轴
    coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)
    geometries.append(coordinate_frame)
    
    print(f"\n准备可视化，几何体数量: {len(geometries)}")
    print(f"可视化说明:")
    print(f"- 灰色点云: 背景点云")
    print(f"- 鲜艳彩色点云: 有旋转角度的实例")
    print(f"- 暗淡彩色点云: 无旋转角度或无bbox标注的实例")
    print(f"- 彩色线框: GT bbox（仅有旋转角度的实例）")
    print(f"- 使用鼠标拖拽旋转视角，滚轮缩放")
    
    print(f"\n正在启动Open3D可视化窗口...")
    try:
        # 显示可视化
        o3d.visualization.draw_geometries(
            geometries,
            window_name=f"Bbox vs Instance Match - {scan_id}",
            width=1200,
            height=800,
            left=50,
            top=50
        )
        print(f"可视化窗口已关闭")
    except Exception as e:
        print(f"Open3D可视化出错: {e}")
        import traceback
        traceback.print_exc()
    
    return all_instances

import argparse

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="可视化ScanNet场景中的Bbox和实例")
    parser.add_argument('scene_id', nargs='?', default=None, help='要可视化的场景ID (例如 scene0000_00)')
    args = parser.parse_args()

    print("开始测试bbox和实例的对应关系...")

    # 创建数据集
    try:
        dataset = ScanNetDataset(
            data_root="datasets/scannet",
            prefix="val",
            # suffix="inst",
            training=False,  # 保持训练模式，用于检查数据增强后bbox和实例的对齐情况
            voxel_cfg={"scale": 50, "spatial_shape": [128, 512], "max_npoint": 250000},
        )
        print(f"数据集加载成功，共有 {len(dataset)} 个样本")
    except Exception as e:
        print(f"数据集加载失败: {e}")
        return

    if args.scene_id:
        try:
            sample_idx = dataset.get_scene_index(args.scene_id)
            print(f"\n{'='*60}")
            print(f"测试场景 {args.scene_id} (样本索引 {sample_idx})")
            print(f"{'='*60}")
            valid_instances = visualize_bbox_instance_match(dataset, sample_idx, max_instances=50)
            print(f"\n场景 {args.scene_id} 可视化完成，显示了 {len(valid_instances)} 个有效实例")
        except ValueError as e:
            print(e)
        except Exception as e:
            print(f"场景 {args.scene_id} 处理失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        # 测试多个样本
        test_samples = [0, 1, 2]  # 可以修改要测试的样本索引
        for sample_idx in test_samples:
            if sample_idx >= len(dataset):
                print(f"样本索引 {sample_idx} 超出范围，跳过")
                continue

            print(f"\n{'='*60}")
            print(f"测试样本 {sample_idx}")
            print(f"{'='*60}")

            try:
                valid_instances = visualize_bbox_instance_match(dataset, sample_idx, max_instances=50)
                print(f"\n样本 {sample_idx} 可视化完成，显示了 {len(valid_instances)} 个有效实例")

                # 询问是否继续
                if sample_idx < test_samples[-1]:
                    user_input = input("\n按Enter继续下一个样本，输入'q'退出: ")
                    if user_input.lower() == 'q':
                        break

            except Exception as e:
                print(f"样本 {sample_idx} 处理失败: {e}")
                import traceback
                traceback.print_exc()

    print("\n测试完成！")

if __name__ == '__main__':
    main()