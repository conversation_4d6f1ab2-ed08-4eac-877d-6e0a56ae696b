#!/bin/bash

# TIMR 模型测试脚本 - 优化版本
# 支持灵活的配置和错误处理

set -e  # 遇到错误立即退出

echo "🧪 TIMR 模型测试"
echo "================================"

# 默认配置
CONFIG_FILE="configs/timr_scannet.yaml"
GPU_ID=0
DEBUG_MODE=false
COMPUTE_PRECISION=true
CONF_THRESHOLDS="0.5 0.25"

# 自动检测最新实验
detect_latest_experiment() {
    if [ -d "experiments" ]; then
        local latest_exp=$(ls -t experiments/ | grep "timr_" | head -n 1)
        if [ -n "$latest_exp" ]; then
            echo "experiments/$latest_exp"
        fi
    fi
}

# 自动检测最佳模型
detect_best_model() {
    local exp_dir=$1
    if [ -d "$exp_dir" ]; then
        # 查找AP最高的模型文件
        local best_model=$(ls "$exp_dir"/*.pth 2>/dev/null | grep -E "epoch.*_AP_" | sort -V | tail -n 1)
        if [ -n "$best_model" ]; then
            echo "$best_model"
        elif [ -f "$exp_dir/best_model.pth" ]; then
            echo "$exp_dir/best_model.pth"
        fi
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
🧪 TIMR 模型测试脚本

用法:
    $0 [选项]

选项:
    -c, --config PATH           配置文件路径 (默认: $CONFIG_FILE)
    -m, --model PATH            模型检查点路径 (自动检测最新)
    -e, --experiment PATH       实验目录路径 (自动检测最新)
    -o, --output PATH           输出目录路径 (默认: 实验目录/output)
    -g, --gpu ID                GPU设备ID (默认: $GPU_ID)
    -d, --debug                 调试模式 (只测试第一个批次)
    --no-precision              不计算精度指标
    --conf_thresholds VALUES    置信度阈值 (默认: $CONF_THRESHOLDS)
    -h, --help                  显示帮助信息

示例:
    $0                                      # 使用默认设置
    $0 -e experiments/timr_20250613_194827  # 指定实验目录
    $0 -m model.pth -o results              # 指定模型和输出
    $0 -d                                   # 调试模式
EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -m|--model)
            MODEL_PATH="$2"
            shift 2
            ;;
        -e|--experiment)
            EXP_DIR="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -g|--gpu)
            GPU_ID="$2"
            shift 2
            ;;
        -d|--debug)
            DEBUG_MODE=true
            shift
            ;;
        --no-precision)
            COMPUTE_PRECISION=false
            shift
            ;;
        --conf-thresholds)
            CONF_THRESHOLDS="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "❌ 未知参数: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo "🔍 检查依赖和环境..."

# 检查Python依赖
python -c "import torch; print('✅ PyTorch 版本:', torch.__version__)" || {
    echo "❌ PyTorch未安装"
    exit 1
}

python -c "import yaml; print('✅ PyYAML已安装')" || {
    echo "❌ PyYAML未安装，正在安装..."
    pip install PyYAML
}

# 检查CUDA
if command -v nvidia-smi > /dev/null; then
    echo "✅ CUDA可用"
    nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits | head -1
else
    echo "⚠️  CUDA不可用，将使用CPU"
fi

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$GPU_ID
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 自动检测实验目录
if [ -z "$EXP_DIR" ]; then
    # EXP_DIR=$(detect_latest_experiment)
    EXP_DIR="experiments/timr_20250726_112438"
    if [ -z "$EXP_DIR" ]; then
        echo "❌ 未找到实验目录，请使用 -e 参数指定"
        exit 1
    fi
    echo "🔍 自动检测到实验目录: $EXP_DIR"
fi

# 自动检测模型文件
if [ -z "$MODEL_PATH" ]; then
    MODEL_PATH=$(detect_best_model "$EXP_DIR")
    if [ -z "$MODEL_PATH" ]; then
        echo "❌ 未找到模型文件，请使用 -m 参数指定"
        exit 1
    fi
    echo "🔍 自动检测到模型文件: $MODEL_PATH"
fi

# 设置输出目录
if [ -z "$OUTPUT_DIR" ]; then
    OUTPUT_DIR="$EXP_DIR/output"
fi

# 验证文件存在性
echo "📋 验证配置..."
[ -f "$CONFIG_FILE" ] || { echo "❌ 配置文件不存在: $CONFIG_FILE"; exit 1; }
[ -f "$MODEL_PATH" ] || { echo "❌ 模型文件不存在: $MODEL_PATH"; exit 1; }

echo "✅ 配置验证通过"
echo "   配置文件: $CONFIG_FILE"  
echo "   模型文件: $MODEL_PATH"
echo "   输出目录: $OUTPUT_DIR"
echo "   GPU设备: $GPU_ID"
echo "   调试模式: $DEBUG_MODE"
echo "   计算精度: $COMPUTE_PRECISION"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 确认开始测试
if [ "$DEBUG_MODE" = false ]; then
    read -p "🚀 是否开始测试? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 测试已取消"
        exit 1
    fi
fi

# 构建命令
CMD="python test.py \"$CONFIG_FILE\" \"$MODEL_PATH\" --out \"$OUTPUT_DIR/\" --gpu $GPU_ID"

if [ "$DEBUG_MODE" = true ]; then
    CMD="$CMD --debug"
    echo "🐛 调试模式已启用"
fi

if [ "$COMPUTE_PRECISION" = true ]; then
    CMD="$CMD --compute-precision --conf-thresholds $CONF_THRESHOLDS"
fi

echo "🎯 开始测试..."
echo "================================"
echo "执行命令: $CMD"
echo "================================"

# 记录开始时间
START_TIME=$(date +%s)

# 执行测试
eval $CMD

# 记录结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "================================"
echo "🎉 测试完成!"
echo "⏱️  耗时: ${DURATION}秒"
echo "📁 结果保存在: $OUTPUT_DIR"

# 显示结果统计
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 结果统计:"
    echo "   输出文件数量: $(find "$OUTPUT_DIR" -type f | wc -l)"
    if [ -d "$OUTPUT_DIR/pred_instance" ]; then
        echo "   预测实例数量: $(find "$OUTPUT_DIR/pred_instance" -name "*.txt" | wc -l)"
    fi
fi

echo "✨ 测试流程结束"