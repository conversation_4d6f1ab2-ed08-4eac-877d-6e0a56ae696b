#!/bin/bash

# TIMR 结果可视化脚本 - 简化版本
# 直接修改下面的配置即可使用

set -e  # 遇到错误立即退出

echo "🎨 TIMR 结果可视化"
echo "================================"

# ===== 配置区域 - 直接修改这里 =====
DATA_ROOT="datasets/scannet"
PREDICTION_PATH="./experiments/timr_20250724_155747/output"  # 修改为你的实验路径
ROOM_NAME="scene0217_00"                                     # 修改为你要可视化的场景
TASK="instance_pred"                                         # instance_pred/instance_gt/origin_pc
CONF_THRESH=0.3                                             # 置信度阈值
# ===================================

# 其他配置
BATCH_MODE=false
VISUALIZATION_MODE="save"  # save 或 interactive

# 获取实验中的场景列表
get_scene_list() {
    local prediction_path=$1
    local pred_instance_dir="$prediction_path/pred_instance"
    if [ -d "$pred_instance_dir" ]; then
        ls "$pred_instance_dir"/*.txt 2>/dev/null | xargs -n1 basename | sed 's/\.txt$//' | sort -u
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
🎨 TIMR 结果可视化脚本

用法:
    $0 [选项]

选项:
    -r, --room-name NAME        覆盖脚本中的场景名称
    -t, --task TYPE             覆盖脚本中的任务类型:
                                  instance_pred  - 预测实例分割 (默认)
                                  instance_gt    - 真实实例分割  
                                  origin_pc      - 原始点云
    -c, --conf-thresh FLOAT     覆盖脚本中的置信度阈值
    -o, --output PATH           指定输出PLY文件路径
    -b, --batch                 批量处理模式 (处理实验中的所有场景)
    -l, --list                  列出可用的场景
    -i, --interactive           交互式可视化 (需要Open3D)
    -h, --help                  显示帮助信息

💡 提示: 主要配置请直接修改脚本开头的配置区域

示例:
    $0                                          # 使用脚本中的默认配置
    $0 -r scene0020_00                         # 只改变场景名
    $0 -r scene0020_00 -t instance_gt          # 改变场景和任务类型
    $0 -b                                       # 批量处理所有场景
    $0 -l                                       # 列出可用场景
EOF
}

# 解析命令行参数 (用于覆盖脚本中的默认配置)
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--room-name)
            ROOM_NAME="$2"
            shift 2
            ;;
        -t|--task)
            TASK="$2"
            shift 2
            ;;
        -c|--conf-thresh)
            CONF_THRESH="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FILE="$2"
            VISUALIZATION_MODE="save"
            shift 2
            ;;
        -b|--batch)
            BATCH_MODE=true
            shift
            ;;
        -l|--list)
            LIST_MODE=true
            shift
            ;;
        -i|--interactive)
            VISUALIZATION_MODE="interactive"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "❌ 未知参数: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo "🔍 检查依赖和环境..."

# 检查Python依赖
python -c "import numpy; print('✅ NumPy已安装')" || {
    echo "❌ NumPy未安装"
    exit 1
}

# 检查Open3D (仅在交互模式需要)
if [ "$VISUALIZATION_MODE" = "interactive" ]; then
    python -c "import open3d; print('✅ Open3D已安装')" || {
        echo "❌ 交互式可视化需要Open3D，正在尝试安装..."
        pip install open3d
    }
fi

# 设置环境变量
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 验证配置
echo "📋 验证配置..."
[ -d "$DATA_ROOT" ] || { echo "❌ 数据根目录不存在: $DATA_ROOT"; exit 1; }

# 验证预测路径
if [ "$TASK" = "instance_pred" ]; then
    [ -d "$PREDICTION_PATH" ] || { echo "❌ 预测结果目录不存在: $PREDICTION_PATH"; exit 1; }
fi

# 列出场景模式
if [ "$LIST_MODE" = true ]; then
    echo "📋 可用场景列表:"
    if [ "$TASK" = "instance_pred" ]; then
        SCENES=$(get_scene_list "$PREDICTION_PATH")
        if [ -n "$SCENES" ]; then
            echo "$SCENES" | nl -w3 -s'. '
        else
            echo "❌ 未找到任何场景结果"
        fi
    else
        echo "🔍 扫描数据目录中的场景..."
        if [ -d "$DATA_ROOT/processed_data" ]; then
            ls "$DATA_ROOT/processed_data" | head -20 | nl -w3 -s'. '
            echo "   ... (显示前20个场景)"
        else
            echo "❌ 数据目录结构不正确"
        fi
    fi
    exit 0
fi

echo "✅ 配置验证通过"
echo "   数据根目录: $DATA_ROOT"
echo "   预测路径: $PREDICTION_PATH"
echo "   任务类型: $TASK"
echo "   置信度阈值: $CONF_THRESH"

# 批量处理模式
if [ "$BATCH_MODE" = true ]; then
    echo "🔄 批量处理模式启动..."
    SCENES=$(get_scene_list "$PREDICTION_PATH")
    if [ -z "$SCENES" ]; then
        echo "❌ 未找到任何场景结果"
        exit 1
    fi
    
    SCENE_COUNT=$(echo "$SCENES" | wc -l)
    echo "📊 找到 $SCENE_COUNT 个场景待处理"
    
    # 获取预测路径的父目录作为输出目录
    EXP_DIR=$(dirname "$PREDICTION_PATH")
    BATCH_OUTPUT_DIR="$EXP_DIR/visualizations"
    mkdir -p "$BATCH_OUTPUT_DIR"
    
    PROCESSED=0
    echo "$SCENES" | while read -r scene; do
        if [ -n "$scene" ]; then
            PROCESSED=$((PROCESSED + 1))
            echo "🎨 [$PROCESSED/$SCENE_COUNT] 处理场景: $scene"
            
            # 输出文件名直接使用场景名
            OUTPUT_PLY="$BATCH_OUTPUT_DIR/${scene}.ply"
            
            python visualize.py \
                --data_root "$DATA_ROOT" \
                --prediction_path "$PREDICTION_PATH" \
                --room_name "$scene" \
                --task "$TASK" \
                --conf_thresh "$CONF_THRESH" \
                --out "$OUTPUT_PLY" \
                || echo "⚠️  场景 $scene 处理失败"
        fi
    done
    
    echo "🎉 批量处理完成!"
    echo "📁 结果保存在: $BATCH_OUTPUT_DIR"
    echo "📊 处理场景数量: $SCENE_COUNT"
    exit 0
fi

# 单场景处理模式
if [ -z "$ROOM_NAME" ]; then
    echo "❌ 请在脚本开头设置ROOM_NAME或使用 -r 参数指定场景名称"
    echo "使用 -l 查看可用场景"
    exit 1
fi

# 验证场景存在性
if [ "$TASK" = "instance_pred" ]; then
    SCENE_FILE="$PREDICTION_PATH/pred_instance/$ROOM_NAME.txt"
    [ -f "$SCENE_FILE" ] || { 
        echo "❌ 场景预测结果不存在: $SCENE_FILE"
        echo "💡 使用 -l 查看可用场景"
        exit 1
    }
else
    DATA_FILE="$DATA_ROOT/processed_data/$ROOM_NAME/data.npz"
    [ -f "$DATA_FILE" ] || {
        echo "❌ 场景数据不存在: $DATA_FILE"
        exit 1
    }
fi

echo "🎯 开始可视化场景: $ROOM_NAME"
echo "================================"

# 构建命令
CMD="python visualize.py --data_root \"$DATA_ROOT\" --prediction_path \"$PREDICTION_PATH\" --room_name \"$ROOM_NAME\" --task \"$TASK\" --conf_thresh $CONF_THRESH"

if [ "$VISUALIZATION_MODE" = "save" ] && [ -n "$OUTPUT_FILE" ]; then
    CMD="$CMD --out \"$OUTPUT_FILE\""
    echo "💾 保存模式: $OUTPUT_FILE"
elif [ "$VISUALIZATION_MODE" = "save" ]; then
    # 输出文件名直接使用场景名
    EXP_DIR=$(dirname "$PREDICTION_PATH")
    OUTPUT_FILE="$EXP_DIR/${ROOM_NAME}.ply"
    CMD="$CMD --out \"$OUTPUT_FILE\""
    echo "💾 保存模式: $OUTPUT_FILE"
else
    echo "👀 交互式可视化模式"
fi

echo "执行命令: $CMD"
echo "================================"

# 记录开始时间
START_TIME=$(date +%s)

# 执行可视化
eval $CMD

# 记录结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "================================"
echo "🎉 可视化完成!"
echo "⏱️  耗时: ${DURATION}秒"

if [ "$VISUALIZATION_MODE" = "save" ]; then
    if [ -f "$OUTPUT_FILE" ]; then
        FILE_SIZE=$(du -h "$OUTPUT_FILE" | cut -f1)
        echo "📁 文件保存在: $OUTPUT_FILE"
        echo "📊 文件大小: $FILE_SIZE"
        echo "💡 可以使用MeshLab、CloudCompare等工具打开PLY文件"
    else
        echo "⚠️  输出文件未生成"
    fi
fi

echo "✨ 可视化流程结束"