#!/usr/bin/env python3
"""
MAFT简化测试脚本
基于QIMR版本改编，用于快速测试和调试
"""

import argparse
import torch
import yaml
import os
import sys
import numpy as np
import multiprocessing as mp
from tqdm import tqdm

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型和数据
from model.maft.model.maft import MAFT
from data import build_dataset, build_dataloader
from utils.logger import get_logger
from model.maft.utils.mask_encoder import rle_decode

# 导入RFS标签系统 - 和DIMR保持一致  
from utils.rfs_labels import RFS_CLASSES

# 对应的NYU_ID映射（前20个是标准NYU20，后5个是新增类别）
RFS_NYU_ID = (
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 24, 28, 33, 34, 36, 39,  # NYU20
    -1, -1, -1, -1, -1  # 新增的5个类别没有NYU_ID对应
)

def generate_mesh(comp_net, pred_instances, scan_id):
    meshes = []
    TIMR_TO_BSP_MAPPING = {
        3: 5,   # cabinet -> cabinet (BSP索引5)
        5: 1,   # chair -> chair (BSP索引1)
        6: 3,   # sofa -> sofa (BSP索引3)
        7: 0,   # table -> table (BSP索引0)
        10: 2,  # bookshelf -> bookshelf (BSP索引2)
        12: 5,  # counter -> cabinet (映射到cabinet，BSP索引5)
        13: 0,  # desk -> table (映射到table，BSP索引0)
        17: 7,  # toilet -> bathtub (映射到bathtub，BSP索引7)
        18: 7,  # sink -> bathtub (映射到bathtub，BSP索引7)
        19: 7,  # bathtub -> bathtub (BSP索引7)
        22: 6,  # display -> display (BSP索引6)
        23: 4,  # trash_bin -> trash_bin (BSP索引4)
    }
    BSP_CLASS_NAMES = ['table', 'chair', 'bookshelf', 'sofa', 'trash_bin', 'cabinet', 'display', 'bathtub']
    valid_instances = []
    for i, inst in enumerate(pred_instances):
        if 'pred_zs' not in inst or 'bboxes' not in inst:
            continue
                
        timr_label = inst['label_id']
        bsp_label = TIMR_TO_BSP_MAPPING.get(timr_label, -1)
            
        if bsp_label == -1:
            continue  # 跳过不支持的类别
                
        valid_instances.append((i, inst, bsp_label))
    
    batch_zs = []
    batch_bboxes = []
    batch_labels = []
    for _, inst, bsp_label in valid_instances:
        batch_zs.append(torch.from_numpy(inst['pred_zs']).float())
        batch_bboxes.append(torch.from_numpy(inst['bboxes']).float())
        batch_labels.append(torch.tensor(bsp_label).long())
    # 转换为batch tensor
    feats = torch.stack(batch_zs)  # [N, 256]
    bboxes = torch.stack(batch_bboxes)  # [N, 7]
    labels = torch.stack(batch_labels)  # [N]
    
    # 移动到GPU
    if torch.cuda.is_available():
        feats = feats.cuda()
        bboxes = bboxes.cuda()
        labels = labels.cuda()
        
    # 准备数据字典 - 按照DIMR的格式
    data = {
        'feats': feats,
        'bboxes': bboxes,
        'labels': labels
    }
    with torch.no_grad():
        result = comp_net(data, phase=1, return_mesh='generate')
    
    for j, (i, inst, bsp_label) in enumerate(valid_instances):
        mesh = result['meshes'][j]
        bsp_class_name = BSP_CLASS_NAMES[bsp_label]
        meshes.append({
                    'scan_id': scan_id,
                    'instance_id': i,
                    'label_id': inst['label_id'],
                    'bsp_label_id': bsp_label,
                    'bsp_class_name': bsp_class_name,
                    'mesh': mesh
                })
    return meshes

def save_meshes(meshes, output_dir, scan_id):
    mesh_dir = os.path.join(output_dir, 'meshes', scan_id)
    os.makedirs(mesh_dir, exist_ok=True)
    for mesh_data in meshes:
        mesh = mesh_data['mesh']
        instance_id = mesh_data['instance_id']
        label_id = mesh_data['label_id']
        bsp_class_name = mesh_data['bsp_class_name']
        mesh_filename = f"{scan_id}_{instance_id:03d}_timr{label_id}_bsp{bsp_class_name}.ply"
        mesh_path = os.path.join(mesh_dir, mesh_filename)
        mesh.export(mesh_path)

def save_single_instance(root, scan_id, insts, nyu_id=None):
    f = open(os.path.join(root, f'{scan_id}.txt'), 'w')
    os.makedirs(os.path.join(root, 'predicted_masks'), exist_ok=True)
    for i, inst in enumerate(insts):
        assert scan_id == inst['scan_id']
        label_id = inst['label_id']
        # scannet dataset use nyu_id for evaluation
        if nyu_id is not None:
            label_id = nyu_id[label_id - 1]
        conf = inst['conf']
        f.write(f'predicted_masks/{scan_id}_{i:03d}.txt {label_id} {conf:.4f}\n')
        mask_path = os.path.join(root, 'predicted_masks', f'{scan_id}_{i:03d}.txt')
        mask = rle_decode(inst['pred_mask'])
        np.savetxt(mask_path, mask, fmt='%d')
    f.close()


def save_pred_instances(root, name, scan_ids, pred_insts, nyu_id=None):
    root = os.path.join(root, name)
    os.makedirs(root, exist_ok=True)
    roots = [root] * len(scan_ids)
    nyu_ids = [nyu_id] * len(scan_ids)
    pool = mp.Pool()
    pool.starmap(save_single_instance, zip(roots, scan_ids, pred_insts, nyu_ids))
    pool.close()
    pool.join()


def save_gt_instance(path, gt_inst, nyu_id=None):
    """保存GT实例"""
    if nyu_id is not None:
        sem = gt_inst // 1000
        ignore = sem == 0
        ins = gt_inst % 1000
        nyu_id = np.array(nyu_id)
        sem = nyu_id[sem - 1]
        sem[ignore] = 0
        gt_inst = sem * 1000 + ins
    np.savetxt(path, gt_inst, fmt='%d')


def save_gt_instances(root, name, scan_ids, gt_insts, nyu_id=None):
    """保存所有GT实例"""
    root = os.path.join(root, name)
    os.makedirs(root, exist_ok=True)
    paths = [os.path.join(root, f'{i}.txt') for i in scan_ids]
    pool = mp.Pool()
    nyu_ids = [nyu_id] * len(scan_ids)
    pool.starmap(save_gt_instance, zip(paths, gt_insts, nyu_ids))
    pool.close()
    pool.join()


def get_args():
    parser = argparse.ArgumentParser('TIMR Simple Test')
    parser.add_argument('config', type=str, help='path to config file')
    parser.add_argument('checkpoint', type=str, help='path to checkpoint')
    parser.add_argument('--out', type=str, help='directory for output results')
    parser.add_argument('--gpu', type=int, default=0, help='GPU device to use')
    parser.add_argument('--debug', action='store_true', help='enable debug mode (only test first batch)')
    parser.add_argument('--compute-precision', action='store_true', default=True, 
                        help='compute precision at fixed confidence thresholds (default: True)')
    parser.add_argument('--conf-thresholds', nargs='+', type=float, default=[0.5, 0.25],
                        help='confidence thresholds for precision computation (default: [0.5, 0.25])')
    args = parser.parse_args()
    return args


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def get_dataset_info(dataset):
    """获取数据集的类别和NYU_ID信息"""
    # 尝试从数据集获取，如果没有则使用标准映射
    classes = getattr(dataset, 'CLASSES', RFS_CLASSES)
    nyu_id = getattr(dataset, 'NYU_ID', RFS_NYU_ID)
    
    return classes, nyu_id


def main():
    args = get_args()
    
    # 设置GPU
    if torch.cuda.is_available():
        torch.cuda.set_device(args.gpu)
    
    # 加载配置
    cfg = load_config(args.config)
    
    # 设置随机种子
    torch.manual_seed(cfg['test']['seed'])
    
    # 设置日志
    logger = get_logger('MAFT-Simple-Test')
    logger.info(f"Using device: cuda:{args.gpu}" if torch.cuda.is_available() else "cpu")
    
    # 构建模型
    logger.info("Building MAFT model...")
    model_config = cfg['model'].copy()
    model_config.pop('name', None)  # 移除name字段，避免传递给MAFT构造函数
    model = MAFT(**model_config)
    model = model.cuda() if torch.cuda.is_available() else model
    
    # 加载检查点
    logger.info(f'Load checkpoint from {args.checkpoint}')
    checkpoint = torch.load(args.checkpoint, map_location='cpu', weights_only=False)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)
    
    # 构建数据加载器
    logger.info("Building test data loader...")
    dataset = build_dataset(cfg['data']['test'], logger)
    dataloader = build_dataloader(dataset, training=False, **cfg['dataloader']['test'])
    
    # 获取数据集信息
    classes, nyu_id = get_dataset_info(dataset)
    logger.info(f"Dataset classes: {classes}")
    logger.info(f"NYU_ID mapping: {nyu_id}")
    logger.info(f"Test samples: {len(dataset)}")
    
    # 开始测试
    results, scan_ids = [], []
    pred_insts, gt_insts = [], []  # 用于评估的实例数据
    all_meshes = []
    
    progress_bar = tqdm(total=len(dataloader))
    with torch.no_grad():
        model.eval()
        for batch_idx, batch in enumerate(dataloader):
            
            # 数据移动到GPU
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    batch[key] = value.cuda() if torch.cuda.is_available() else value
            
            # 模型推理
            result = model(batch, mode='predict')
            
            # 保存结果
            results.append(result)
            
            # 收集评估用的实例数据
            if 'pred_instances' in result:
                pred_insts.append(result['pred_instances'])
                
                # 只有置信度大于0.5的实例才生成mesh
                # if 'conf' in result['pred_instances'][0]:
                #     meshes = generate_mesh(model.decoder.comp_net, result['pred_instances'], result['scan_id'])
                #     all_meshes.extend(meshes)
                # else:
                #     print("No confidence in pred_instances")
                #     all_meshes.append([])
            else:
                pred_insts.append([])
                
            if 'gt_instances' in result:
                gt_insts.append(result['gt_instances'])
            else:
                gt_insts.append([])
            
            progress_bar.update()
            
            # 如果是调试模式，只测试一个batch就退出
            if args.debug:
                logger.info("Debug mode enabled, testing only first batch")
                break
        
        progress_bar.close()
    
    # 收集所有结果
    for res in results:
        scan_ids.append(res.get('scan_id', f'scene_{len(scan_ids)}'))
    
    # 统计信息
    total_pred_instances = sum(len(pred) for pred in pred_insts)
    total_gt_instances = sum(len(gt) for gt in gt_insts if gt is not None)
    
    logger.info("=== 测试结果统计 ===")
    logger.info(f"总场景数: {len(results)}")
    logger.info(f"总预测实例数: {total_pred_instances}")
    logger.info(f"总GT实例数: {total_gt_instances}")
    logger.info(f"平均每场景预测实例数: {total_pred_instances/len(results):.2f}")
    logger.info(f"总生成mesh数: {len(all_meshes)}")
    
    # 强制进行评估
    logger.info('开始评估实例分割性能...')
    try:
        from model.maft.evaluation import ScanNetEval
        scannet_eval = ScanNetEval(classes)
        
        logger.info(f"准备评估: pred_insts数量={len(pred_insts)}, gt_insts数量={len(gt_insts)}")
        
        # 检查数据格式
        if pred_insts and len(pred_insts) > 0:
            logger.info(f"第一个pred_insts长度: {len(pred_insts[0])}")
        if gt_insts and len(gt_insts) > 0:
            logger.info(f"第一个gt_insts类型: {type(gt_insts[0])}")
            if hasattr(gt_insts[0], 'shape'):
                logger.info(f"第一个gt_insts shape: {gt_insts[0].shape}")
        
        eval_res = scannet_eval.evaluate(pred_insts, gt_insts, 
                                       compute_precision=args.compute_precision, 
                                       conf_thresholds=args.conf_thresholds)
        
        logger.info('=== 评估结果 ===')
        logger.info('AP: {:.3f}. AP_50: {:.3f}. AP_25: {:.3f}'.format(
            eval_res['all_ap'], eval_res['all_ap_50%'], eval_res['all_ap_25%']))
        
        # 打印Precision结果（如果计算了的话）
        if args.compute_precision:
            logger.info('=== Precision @ 固定阈值 ===')
            for conf_th in args.conf_thresholds:
                prec_50_key = f'prec_50%_conf_{conf_th}'
                prec_25_key = f'prec_25%_conf_{conf_th}'
                prec_50_val = eval_res.get(prec_50_key, 0.0)
                prec_25_val = eval_res.get(prec_25_key, 0.0)
                logger.info(f'Prec@0.5IoU_{conf_th}Conf: {prec_50_val:.3f}')
                logger.info(f'Prec@0.25IoU_{conf_th}Conf: {prec_25_val:.3f}')
        
        # 保存评估结果到文件
        if args.out:
            eval_output = os.path.join(args.out, 'evaluation_results.txt')
            with open(eval_output, 'w') as f:
                f.write("TIMR Evaluation Results\n")
                f.write("=" * 30 + "\n")
                f.write(f"AP: {eval_res['all_ap']:.3f}\n")
                f.write(f"AP_50: {eval_res['all_ap_50%']:.3f}\n")
                f.write(f"AP_25: {eval_res['all_ap_25%']:.3f}\n")
                
                # 保存Precision结果（如果计算了的话）
                if args.compute_precision:
                    f.write("\nPrecision @ Fixed Thresholds:\n")
                    for conf_th in args.conf_thresholds:
                        prec_50_key = f'prec_50%_conf_{conf_th}'
                        prec_25_key = f'prec_25%_conf_{conf_th}'
                        prec_50_val = eval_res.get(prec_50_key, 0.0)
                        prec_25_val = eval_res.get(prec_25_key, 0.0)
                        f.write(f"Prec@0.5IoU_{conf_th}Conf: {prec_50_val:.3f}\n")
                        f.write(f"Prec@0.25IoU_{conf_th}Conf: {prec_25_val:.3f}\n")
                f.write("\n详细结果:\n")
                for key, value in eval_res.items():
                    if isinstance(value, dict):
                        f.write(f"{key}:\n")
                        for k, v in value.items():
                            f.write(f"  {k}: {v}\n")
                    else:
                        f.write(f"{key}: {value}\n")
            logger.info(f"评估结果已保存到: {eval_output}")
        
    except Exception as e:
        logger.error(f"评估过程中出现错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        logger.info("跳过评估...")
    
    # 保存结果（如果指定了输出目录）
    if args.out:
        logger.info(f'Save results to {args.out}')
        
        # 保存预测实例（与QIMR格式一致）
        save_pred_instances(args.out, 'pred_instance', scan_ids, pred_insts, nyu_id)
        
        # 保存GT实例（如果不是test数据集）
        if not cfg['data']['test']['prefix'] == 'test':
            save_gt_instances(args.out, 'gt_instance', scan_ids, gt_insts, nyu_id)
            logger.info(f"GT实例已保存到: {args.out}/gt_instance")
            # 保存mesh
            # meshes_by_scene = {}
            # for mesh in all_meshes:
            #     scan_id = mesh['scan_id']
            #     if scan_id not in meshes_by_scene:
            #         meshes_by_scene[scan_id] = []
            #     meshes_by_scene[scan_id].append(mesh)
            # for scan_id, meshes in meshes_by_scene.items():
            #     save_meshes(meshes, args.out, scan_id)
            # logger.info(f"Mesh已保存到: {args.out}/meshes")
        
        # 保存简单的统计信息
        with open(os.path.join(args.out, 'test_summary.txt'), 'w') as f:
            f.write("TIMR Simple Test Results\n")
            f.write("=" * 30 + "\n")
            f.write(f"Total scenes: {len(results)}\n")
            f.write(f"Total predicted instances: {total_pred_instances}\n")
            f.write(f"Total GT instances: {total_gt_instances}\n")
            f.write(f"Average instances per scene: {total_pred_instances/len(results):.2f}\n")
            f.write(f"Classes: {classes}\n")
            f.write(f"NYU_ID mapping: {nyu_id}\n")
        
        logger.info("Results saved successfully!")
    
    logger.info("Testing completed!")


if __name__ == '__main__':
    main()